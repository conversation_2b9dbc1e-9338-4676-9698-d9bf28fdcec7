-- قاعدة بيانات مركز التدريب وخدمة المجتمع
-- Training and Community Service Center Database

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS training_center CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE training_center;

-- جدول المستخدمين (الإداريين)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'staff') DEFAULT 'staff',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- جدول الكورسات
CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_name VARCHAR(100) NOT NULL,
    course_code VARCHAR(20) UNIQUE,
    description TEXT,
    duration_hours INT,
    price DECIMAL(10,2),
    instructor_name VARCHAR(100),
    max_students INT DEFAULT 30,
    start_date DATE,
    end_date DATE,
    status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الطلاب
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL,
    university_id VARCHAR(50) NOT NULL UNIQUE,
    course_id INT,
    payment_image VARCHAR(255),
    id_card_image VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(100),
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    attendance_status ENUM('enrolled', 'completed', 'dropped') DEFAULT 'enrolled',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_university_id (university_id),
    INDEX idx_course_id (course_id),
    INDEX idx_full_name (full_name)
);

-- جدول سجل العمليات (للمراجعة والأمان)
CREATE TABLE IF NOT EXISTS activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(50) NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- إدراج مستخدم افتراضي (admin)
-- كلمة المرور: admin123 (مشفرة بـ password_hash)
INSERT INTO users (username, password, full_name, email, role) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 'admin');

-- إدراج بعض الكورسات التجريبية
INSERT INTO courses (course_name, course_code, description, duration_hours, price, instructor_name, max_students, start_date, end_date) VALUES 
('دورة البرمجة الأساسية', 'PROG101', 'دورة تعليم أساسيات البرمجة للمبتدئين', 40, 500.00, 'د. أحمد محمد', 25, '2024-01-15', '2024-02-15'),
('دورة التصميم الجرافيكي', 'DESIGN101', 'دورة تعليم أساسيات التصميم الجرافيكي', 30, 400.00, 'أ. فاطمة علي', 20, '2024-01-20', '2024-02-10'),
('دورة إدارة المشاريع', 'PM101', 'دورة في إدارة المشاريع وفق المعايير الدولية', 35, 600.00, 'د. محمد سالم', 30, '2024-02-01', '2024-02-25'),
('دورة اللغة الإنجليزية', 'ENG101', 'دورة تحسين مهارات اللغة الإنجليزية', 50, 350.00, 'أ. سارة أحمد', 35, '2024-01-10', '2024-03-10'),
('دورة المحاسبة المالية', 'ACC101', 'دورة أساسيات المحاسبة المالية', 45, 550.00, 'د. خالد يوسف', 25, '2024-02-05', '2024-03-05');

-- إنشاء مجلد لحفظ الملفات المرفوعة
-- هذا سيتم إنشاؤه في PHP
