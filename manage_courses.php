<?php
/**
 * صفحة إدارة الكورسات
 * Manage Courses Page
 */

require_once 'config/database.php';
require_once 'config/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$error_message = '';
$success_message = '';

// معالجة إضافة/تعديل الكورس
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    $action = $_POST['action'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'خطأ في التحقق من الأمان';
    } else {
        // تنظيف البيانات
        $course_name = sanitizeInput($_POST['course_name'] ?? '');
        $course_code = sanitizeInput($_POST['course_code'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        $duration_hours = (int)($_POST['duration_hours'] ?? 0);
        $price = (float)($_POST['price'] ?? 0);
        $instructor_name = sanitizeInput($_POST['instructor_name'] ?? '');
        $max_students = (int)($_POST['max_students'] ?? 30);
        $start_date = $_POST['start_date'] ?? '';
        $end_date = $_POST['end_date'] ?? '';
        $status = sanitizeInput($_POST['status'] ?? 'active');
        
        // التحقق من البيانات المطلوبة
        if (empty($course_name)) {
            $error_message = 'يرجى إدخال اسم الكورس';
        } elseif (empty($course_code)) {
            $error_message = 'يرجى إدخال رمز الكورس';
        } elseif ($duration_hours <= 0) {
            $error_message = 'يرجى إدخال عدد ساعات صحيح';
        } elseif ($price < 0) {
            $error_message = 'يرجى إدخال سعر صحيح';
        } elseif ($max_students <= 0) {
            $error_message = 'يرجى إدخال عدد طلاب أقصى صحيح';
        } else {
            try {
                if ($action === 'add') {
                    // التحقق من عدم تكرار رمز الكورس
                    $existing_course = getDB()->fetch("SELECT id FROM courses WHERE course_code = ?", [$course_code]);
                    if ($existing_course) {
                        $error_message = 'رمز الكورس موجود مسبقاً';
                    } else {
                        // إضافة كورس جديد
                        $sql = "INSERT INTO courses (course_name, course_code, description, duration_hours, price, instructor_name, max_students, start_date, end_date, status) 
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                        
                        $params = [
                            $course_name, $course_code, $description, $duration_hours, $price,
                            $instructor_name, $max_students, 
                            !empty($start_date) ? $start_date : null,
                            !empty($end_date) ? $end_date : null,
                            $status
                        ];
                        
                        $course_id = getDB()->insert($sql, $params);
                        
                        // تسجيل العملية
                        logActivity($_SESSION['user_id'], 'add_course', 'courses', $course_id, null, [
                            'course_name' => $course_name,
                            'course_code' => $course_code
                        ]);
                        
                        $success_message = 'تم إضافة الكورس بنجاح';
                    }
                } elseif ($action === 'edit') {
                    $course_id = (int)($_POST['course_id'] ?? 0);
                    
                    if ($course_id <= 0) {
                        $error_message = 'معرف الكورس غير صحيح';
                    } else {
                        // التحقق من وجود الكورس
                        $existing_course = getDB()->fetch("SELECT * FROM courses WHERE id = ?", [$course_id]);
                        if (!$existing_course) {
                            $error_message = 'الكورس غير موجود';
                        } else {
                            // التحقق من عدم تكرار رمز الكورس (باستثناء الكورس الحالي)
                            $duplicate_course = getDB()->fetch("SELECT id FROM courses WHERE course_code = ? AND id != ?", [$course_code, $course_id]);
                            if ($duplicate_course) {
                                $error_message = 'رمز الكورس موجود مسبقاً';
                            } else {
                                // تحديث الكورس
                                $sql = "UPDATE courses SET 
                                        course_name = ?, course_code = ?, description = ?, duration_hours = ?, 
                                        price = ?, instructor_name = ?, max_students = ?, start_date = ?, 
                                        end_date = ?, status = ?, updated_at = CURRENT_TIMESTAMP 
                                        WHERE id = ?";
                                
                                $params = [
                                    $course_name, $course_code, $description, $duration_hours, $price,
                                    $instructor_name, $max_students,
                                    !empty($start_date) ? $start_date : null,
                                    !empty($end_date) ? $end_date : null,
                                    $status, $course_id
                                ];
                                
                                getDB()->update($sql, $params);
                                
                                // تسجيل العملية
                                logActivity($_SESSION['user_id'], 'edit_course', 'courses', $course_id, $existing_course, [
                                    'course_name' => $course_name,
                                    'course_code' => $course_code
                                ]);
                                
                                $success_message = 'تم تحديث الكورس بنجاح';
                            }
                        }
                    }
                }
            } catch (Exception $e) {
                error_log("Course Management Error: " . $e->getMessage());
                $error_message = 'حدث خطأ أثناء معالجة الطلب';
            }
        }
    }
}

// معالجة حذف الكورس
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_action']) && $_POST['delete_action'] === 'delete') {
    $course_id = (int)($_POST['delete_course_id'] ?? 0);
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'خطأ في التحقق من الأمان';
    } elseif ($course_id <= 0) {
        $error_message = 'معرف الكورس غير صحيح';
    } else {
        try {
            // التحقق من وجود طلاب مسجلين في الكورس
            $students_count = getDB()->fetch("SELECT COUNT(*) as count FROM students WHERE course_id = ?", [$course_id])['count'];
            
            if ($students_count > 0) {
                $error_message = "لا يمكن حذف الكورس لأن هناك $students_count طالب مسجل فيه";
            } else {
                // الحصول على بيانات الكورس قبل الحذف
                $course = getDB()->fetch("SELECT * FROM courses WHERE id = ?", [$course_id]);
                
                if (!$course) {
                    $error_message = 'الكورس غير موجود';
                } else {
                    // حذف الكورس
                    $deleted = getDB()->delete("DELETE FROM courses WHERE id = ?", [$course_id]);
                    
                    if ($deleted > 0) {
                        // تسجيل العملية
                        logActivity($_SESSION['user_id'], 'delete_course', 'courses', $course_id, $course, null);
                        
                        $success_message = 'تم حذف الكورس بنجاح';
                    } else {
                        $error_message = 'فشل في حذف الكورس';
                    }
                }
            }
        } catch (Exception $e) {
            error_log("Delete Course Error: " . $e->getMessage());
            $error_message = 'حدث خطأ أثناء حذف الكورس';
        }
    }
}

// الحصول على قائمة الكورسات
try {
    $courses = getDB()->fetchAll("
        SELECT c.*, 
               COUNT(s.id) as students_count,
               SUM(CASE WHEN s.payment_status = 'paid' THEN 1 ELSE 0 END) as paid_students
        FROM courses c 
        LEFT JOIN students s ON c.id = s.course_id 
        GROUP BY c.id 
        ORDER BY c.created_at DESC
    ");
} catch (Exception $e) {
    error_log("Courses List Error: " . $e->getMessage());
    $courses = [];
}

// الحصول على بيانات الكورس للتعديل
$edit_course = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    try {
        $edit_course = getDB()->fetch("SELECT * FROM courses WHERE id = ?", [$edit_id]);
    } catch (Exception $e) {
        error_log("Edit Course Error: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الكورسات - مركز التدريب وخدمة المجتمع</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-book me-2"></i>
                        إدارة الكورسات
                        <span class="badge bg-primary"><?php echo count($courses); ?></span>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#courseModal">
                            <i class="bi bi-plus-circle me-1"></i>
                            إضافة كورس جديد
                        </button>
                    </div>
                </div>
                
                <!-- Alerts -->
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php endif; ?>

                <!-- Courses Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list me-2"></i>
                            قائمة الكورسات
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($courses)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>#</th>
                                            <th>اسم الكورس</th>
                                            <th>الرمز</th>
                                            <th>المدرب</th>
                                            <th>المدة</th>
                                            <th>السعر</th>
                                            <th>الطلاب</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($courses as $index => $course): ?>
                                            <tr>
                                                <td><?php echo $index + 1; ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($course['course_name']); ?></strong>
                                                    <?php if (!empty($course['description'])): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars(substr($course['description'], 0, 50)) . '...'; ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo htmlspecialchars($course['course_code']); ?></span>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($course['instructor_name'] ?? 'غير محدد'); ?>
                                                </td>
                                                <td>
                                                    <?php echo $course['duration_hours']; ?> ساعة
                                                </td>
                                                <td>
                                                    <?php echo number_format($course['price'], 2); ?> ريال
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $course['students_count']; ?></span>
                                                    /
                                                    <span class="badge bg-success"><?php echo $course['paid_students']; ?></span>
                                                    <br><small class="text-muted">مسجل/مدفوع</small>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status = $course['status'];
                                                    $badge_class = '';
                                                    $status_text = '';

                                                    switch ($status) {
                                                        case 'active':
                                                            $badge_class = 'bg-success';
                                                            $status_text = 'نشط';
                                                            break;
                                                        case 'inactive':
                                                            $badge_class = 'bg-warning';
                                                            $status_text = 'غير نشط';
                                                            break;
                                                        case 'completed':
                                                            $badge_class = 'bg-info';
                                                            $status_text = 'مكتمل';
                                                            break;
                                                        default:
                                                            $badge_class = 'bg-secondary';
                                                            $status_text = 'غير محدد';
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $badge_class; ?>"><?php echo $status_text; ?></span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <!-- عرض التفاصيل -->
                                                        <button type="button"
                                                                class="btn btn-sm btn-outline-info"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#courseDetailsModal<?php echo $course['id']; ?>">
                                                            <i class="bi bi-eye"></i>
                                                        </button>

                                                        <!-- تعديل -->
                                                        <button type="button"
                                                                class="btn btn-sm btn-outline-warning"
                                                                onclick="editCourse(<?php echo htmlspecialchars(json_encode($course)); ?>)">
                                                            <i class="bi bi-pencil"></i>
                                                        </button>

                                                        <!-- حذف -->
                                                        <?php if ($course['students_count'] == 0): ?>
                                                            <button type="button"
                                                                    class="btn btn-sm btn-outline-danger"
                                                                    onclick="confirmDelete(<?php echo $course['id']; ?>, '<?php echo htmlspecialchars($course['course_name'], ENT_QUOTES); ?>')">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button type="button"
                                                                    class="btn btn-sm btn-outline-secondary"
                                                                    disabled
                                                                    title="لا يمكن حذف الكورس لوجود طلاب مسجلين">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="bi bi-book display-1 text-muted"></i>
                                <h4 class="mt-3">لا توجد كورسات</h4>
                                <p class="text-muted">لم يتم إضافة أي كورسات بعد</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#courseModal">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    إضافة كورس جديد
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Course Modal (Add/Edit) -->
    <div class="modal fade" id="courseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="courseModalTitle">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة كورس جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="" id="courseForm">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="add" id="courseAction">
                        <input type="hidden" name="course_id" value="" id="courseId">

                        <div class="row">
                            <!-- اسم الكورس -->
                            <div class="col-md-6 mb-3">
                                <label for="course_name" class="form-label">
                                    اسم الكورس <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="course_name" name="course_name" required>
                            </div>

                            <!-- رمز الكورس -->
                            <div class="col-md-6 mb-3">
                                <label for="course_code" class="form-label">
                                    رمز الكورس <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="course_code" name="course_code" required>
                            </div>
                        </div>

                        <!-- الوصف -->
                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <!-- عدد الساعات -->
                            <div class="col-md-4 mb-3">
                                <label for="duration_hours" class="form-label">
                                    عدد الساعات <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" id="duration_hours" name="duration_hours" min="1" required>
                            </div>

                            <!-- السعر -->
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">
                                    السعر (ريال) <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" required>
                            </div>

                            <!-- العدد الأقصى للطلاب -->
                            <div class="col-md-4 mb-3">
                                <label for="max_students" class="form-label">
                                    العدد الأقصى للطلاب <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" id="max_students" name="max_students" min="1" value="30" required>
                            </div>
                        </div>

                        <!-- اسم المدرب -->
                        <div class="mb-3">
                            <label for="instructor_name" class="form-label">اسم المدرب</label>
                            <input type="text" class="form-control" id="instructor_name" name="instructor_name">
                        </div>

                        <div class="row">
                            <!-- تاريخ البداية -->
                            <div class="col-md-4 mb-3">
                                <label for="start_date" class="form-label">تاريخ البداية</label>
                                <input type="date" class="form-control" id="start_date" name="start_date">
                            </div>

                            <!-- تاريخ النهاية -->
                            <div class="col-md-4 mb-3">
                                <label for="end_date" class="form-label">تاريخ النهاية</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                            </div>

                            <!-- الحالة -->
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="completed">مكتمل</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" id="courseSubmitBtn">
                            <i class="bi bi-save me-1"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Course Details Modals -->
    <?php foreach ($courses as $course): ?>
        <div class="modal fade" id="courseDetailsModal<?php echo $course['id']; ?>" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-book me-2"></i>
                            تفاصيل الكورس: <?php echo htmlspecialchars($course['course_name']); ?>
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>المعلومات الأساسية</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>اسم الكورس:</strong></td>
                                        <td><?php echo htmlspecialchars($course['course_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>رمز الكورس:</strong></td>
                                        <td><?php echo htmlspecialchars($course['course_code']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>المدرب:</strong></td>
                                        <td><?php echo htmlspecialchars($course['instructor_name'] ?? 'غير محدد'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>عدد الساعات:</strong></td>
                                        <td><?php echo $course['duration_hours']; ?> ساعة</td>
                                    </tr>
                                    <tr>
                                        <td><strong>السعر:</strong></td>
                                        <td><?php echo number_format($course['price'], 2); ?> ريال</td>
                                    </tr>
                                    <tr>
                                        <td><strong>العدد الأقصى:</strong></td>
                                        <td><?php echo $course['max_students']; ?> طالب</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الحالة:</strong></td>
                                        <td>
                                            <?php
                                            switch ($course['status']) {
                                                case 'active': echo '<span class="badge bg-success">نشط</span>'; break;
                                                case 'inactive': echo '<span class="badge bg-warning">غير نشط</span>'; break;
                                                case 'completed': echo '<span class="badge bg-info">مكتمل</span>'; break;
                                                default: echo '<span class="badge bg-secondary">غير محدد</span>';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>إحصائيات الطلاب</h6>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="h4 text-primary"><?php echo $course['students_count']; ?></div>
                                                <small>إجمالي المسجلين</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="h4 text-success"><?php echo $course['paid_students']; ?></div>
                                                <small>دفعوا الرسوم</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="h4 text-info"><?php echo $course['max_students'] - $course['students_count']; ?></div>
                                                <small>أماكن متاحة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <h6 class="mt-3">التواريخ</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>تاريخ البداية:</strong></td>
                                        <td><?php echo $course['start_date'] ? formatDateArabic($course['start_date']) : 'غير محدد'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاريخ النهاية:</strong></td>
                                        <td><?php echo $course['end_date'] ? formatDateArabic($course['end_date']) : 'غير محدد'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاريخ الإنشاء:</strong></td>
                                        <td><?php echo formatDateArabic($course['created_at']); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <?php if (!empty($course['description'])): ?>
                            <div class="mt-3">
                                <h6>الوصف</h6>
                                <div class="border p-3 rounded bg-light">
                                    <?php echo nl2br(htmlspecialchars($course['description'])); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button"
                                class="btn btn-warning"
                                onclick="editCourse(<?php echo htmlspecialchars(json_encode($course)); ?>)"
                                data-bs-dismiss="modal">
                            <i class="bi bi-pencil me-1"></i>
                            تعديل
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                        تأكيد الحذف
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف الكورس <strong id="courseNameToDelete"></strong>؟</p>
                    <p class="text-danger">
                        <i class="bi bi-exclamation-triangle me-1"></i>
                        هذا الإجراء لا يمكن التراجع عنه.
                    </p>
                </div>
                <div class="modal-footer">
                    <form method="POST" action="" id="deleteForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="delete_action" value="delete">
                        <input type="hidden" name="delete_course_id" id="courseIdToDelete">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-trash me-1"></i>
                            حذف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function editCourse(course) {
            // تغيير عنوان النموذج
            document.getElementById('courseModalTitle').innerHTML = '<i class="bi bi-pencil me-2"></i>تعديل الكورس';
            document.getElementById('courseSubmitBtn').innerHTML = '<i class="bi bi-save me-1"></i>تحديث';

            // تعبئة البيانات
            document.getElementById('courseAction').value = 'edit';
            document.getElementById('courseId').value = course.id;
            document.getElementById('course_name').value = course.course_name;
            document.getElementById('course_code').value = course.course_code;
            document.getElementById('description').value = course.description || '';
            document.getElementById('duration_hours').value = course.duration_hours;
            document.getElementById('price').value = course.price;
            document.getElementById('max_students').value = course.max_students;
            document.getElementById('instructor_name').value = course.instructor_name || '';
            document.getElementById('start_date').value = course.start_date || '';
            document.getElementById('end_date').value = course.end_date || '';
            document.getElementById('status').value = course.status;

            // عرض النموذج
            const modal = new bootstrap.Modal(document.getElementById('courseModal'));
            modal.show();
        }

        function confirmDelete(courseId, courseName) {
            document.getElementById('courseIdToDelete').value = courseId;
            document.getElementById('courseNameToDelete').textContent = courseName;

            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        // إعادة تعيين النموذج عند إغلاقه
        document.getElementById('courseModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('courseForm').reset();
            document.getElementById('courseModalTitle').innerHTML = '<i class="bi bi-plus-circle me-2"></i>إضافة كورس جديد';
            document.getElementById('courseSubmitBtn').innerHTML = '<i class="bi bi-save me-1"></i>حفظ';
            document.getElementById('courseAction').value = 'add';
            document.getElementById('courseId').value = '';
        });

        // التحقق من تواريخ البداية والنهاية
        document.getElementById('start_date').addEventListener('change', function() {
            const startDate = this.value;
            const endDateField = document.getElementById('end_date');

            if (startDate) {
                endDateField.min = startDate;
            }
        });

        document.getElementById('end_date').addEventListener('change', function() {
            const endDate = this.value;
            const startDateField = document.getElementById('start_date');

            if (endDate) {
                startDateField.max = endDate;
            }
        });
    </script>
</body>
</html>
