<?php
/**
 * ملف تشخيص مشاكل تسجيل الدخول
 * Login Debug File
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص مشاكل تسجيل الدخول</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>
    <div class='container mt-5'>
        <div class='card'>
            <div class='card-header bg-primary text-white'>
                <h4>تشخيص مشاكل تسجيل الدخول</h4>
            </div>
            <div class='card-body'>";

try {
    echo "<h5>1. اختبار الاتصال بقاعدة البيانات:</h5>";
    
    // تضمين ملفات الإعدادات
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        echo "<p class='text-success'>✓ تم تحميل ملف الإعدادات</p>";
    } else {
        echo "<p class='text-danger'>✗ ملف config/database.php غير موجود</p>";
        exit;
    }
    
    if (file_exists('config/functions.php')) {
        require_once 'config/functions.php';
        echo "<p class='text-success'>✓ تم تحميل ملف الدوال</p>";
    } else {
        echo "<p class='text-danger'>✗ ملف config/functions.php غير موجود</p>";
        exit;
    }
    
    // اختبار الاتصال
    $database = new Database();
    $db = $database->connect();
    echo "<p class='text-success'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    echo "<h5>2. التحقق من وجود جدول المستخدمين:</h5>";
    
    // التحقق من وجود جدول users
    $stmt = $db->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='text-success'>✓ جدول المستخدمين موجود</p>";
        
        // عرض بيانات المستخدمين
        echo "<h5>3. المستخدمين الموجودين:</h5>";
        $users = getDB()->fetchAll("SELECT id, username, full_name, role, is_active FROM users");
        
        if (count($users) > 0) {
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>ID</th><th>اسم المستخدم</th><th>الاسم الكامل</th><th>الدور</th><th>نشط</th></tr></thead>";
            echo "<tbody>";
            foreach ($users as $user) {
                $active_status = $user['is_active'] ? 'نعم' : 'لا';
                $active_class = $user['is_active'] ? 'text-success' : 'text-danger';
                echo "<tr>";
                echo "<td>{$user['id']}</td>";
                echo "<td><strong>{$user['username']}</strong></td>";
                echo "<td>{$user['full_name']}</td>";
                echo "<td>{$user['role']}</td>";
                echo "<td class='$active_class'>$active_status</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            
            // اختبار كلمة المرور للمستخدم admin
            echo "<h5>4. اختبار كلمة المرور:</h5>";
            $admin_user = getDB()->fetch("SELECT * FROM users WHERE username = 'admin'");
            
            if ($admin_user) {
                echo "<p class='text-info'>المستخدم admin موجود</p>";
                
                // اختبار كلمة المرور
                $test_password = 'admin123';
                if (password_verify($test_password, $admin_user['password'])) {
                    echo "<p class='text-success'>✓ كلمة المرور 'admin123' صحيحة</p>";
                } else {
                    echo "<p class='text-danger'>✗ كلمة المرور 'admin123' غير صحيحة</p>";
                    echo "<p class='text-warning'>سأقوم بإعادة تعيين كلمة المرور...</p>";
                    
                    // إعادة تعيين كلمة المرور
                    $new_password_hash = password_hash('admin123', PASSWORD_DEFAULT);
                    getDB()->update("UPDATE users SET password = ? WHERE username = 'admin'", [$new_password_hash]);
                    echo "<p class='text-success'>✓ تم إعادة تعيين كلمة المرور إلى 'admin123'</p>";
                }
                
                // التحقق من حالة النشاط
                if ($admin_user['is_active']) {
                    echo "<p class='text-success'>✓ المستخدم نشط</p>";
                } else {
                    echo "<p class='text-warning'>المستخدم غير نشط، سأقوم بتفعيله...</p>";
                    getDB()->update("UPDATE users SET is_active = 1 WHERE username = 'admin'");
                    echo "<p class='text-success'>✓ تم تفعيل المستخدم</p>";
                }
                
            } else {
                echo "<p class='text-danger'>✗ المستخدم admin غير موجود</p>";
                echo "<p class='text-info'>سأقوم بإنشاء المستخدم...</p>";
                
                // إنشاء المستخدم admin
                $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
                $sql = "INSERT INTO users (username, password, full_name, email, role, is_active) VALUES (?, ?, ?, ?, ?, ?)";
                $params = ['admin', $password_hash, 'مدير النظام', '<EMAIL>', 'admin', 1];
                
                getDB()->insert($sql, $params);
                echo "<p class='text-success'>✓ تم إنشاء المستخدم admin بنجاح</p>";
            }
            
        } else {
            echo "<p class='text-warning'>لا توجد مستخدمين في النظام</p>";
            echo "<p class='text-info'>سأقوم بإنشاء المستخدم الافتراضي...</p>";
            
            // إنشاء المستخدم الافتراضي
            $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (username, password, full_name, email, role, is_active) VALUES (?, ?, ?, ?, ?, ?)";
            $params = ['admin', $password_hash, 'مدير النظام', '<EMAIL>', 'admin', 1];
            
            getDB()->insert($sql, $params);
            echo "<p class='text-success'>✓ تم إنشاء المستخدم admin بنجاح</p>";
        }
        
    } else {
        echo "<p class='text-danger'>✗ جدول المستخدمين غير موجود</p>";
        echo "<p class='text-info'>يجب تشغيل setup.php أولاً لإنشاء الجداول</p>";
        echo "<a href='setup.php' class='btn btn-primary'>تشغيل الإعداد</a>";
    }
    
    echo "<h5>5. اختبار تسجيل الدخول:</h5>";
    echo "<div class='alert alert-info'>";
    echo "<h6>بيانات تسجيل الدخول:</h6>";
    echo "<p><strong>اسم المستخدم:</strong> admin</p>";
    echo "<p><strong>كلمة المرور:</strong> admin123</p>";
    echo "<a href='login.php' class='btn btn-success'>جرب تسجيل الدخول الآن</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>خطأ:</h5>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h6>الحلول المقترحة:</h6>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li>تحقق من إعدادات قاعدة البيانات في config/database.php</li>";
    echo "<li>شغّل setup.php لإنشاء قاعدة البيانات</li>";
    echo "</ul>";
    echo "<a href='setup.php' class='btn btn-primary'>تشغيل الإعداد</a>";
    echo "</div>";
}

echo "        </div>
        </div>
    </div>
</body>
</html>";
?>
