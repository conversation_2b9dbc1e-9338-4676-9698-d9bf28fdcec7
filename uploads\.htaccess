# حماية مجلد الملفات المرفوعة
# Protect uploads directory

# منع تنفيذ ملفات PHP
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# منع تنفيذ ملفات خطيرة أخرى
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# السماح بأنواع الملفات المحددة فقط
<FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# منع عرض محتويات المجلد
Options -Indexes

# إعدادات الأمان الإضافية
<IfModule mod_headers.c>
    Header set X-Content-Type-Options nosniff
    Header set X-Frame-Options DENY
    Header set X-XSS-Protection "1; mode=block"
</IfModule>
