<?php
/**
 * صفحة إضافة طالب جديد
 * Add New Student Page
 */

require_once 'config/database.php';
require_once 'config/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$error_message = '';
$success_message = '';

// الحصول على قائمة الكورسات
try {
    $courses = getDB()->fetchAll("SELECT id, course_name, course_code FROM courses WHERE status = 'active' ORDER BY course_name");
} catch (Exception $e) {
    error_log("Courses Error: " . $e->getMessage());
    $courses = [];
}

// معالجة إضافة الطالب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // التحقق من CSRF Token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'خطأ في التحقق من الأمان';
    } else {
        // تنظيف البيانات
        $full_name = sanitizeInput($_POST['full_name'] ?? '');
        $university_id = sanitizeInput($_POST['university_id'] ?? '');
        $course_id = (int)($_POST['course_id'] ?? 0);
        $phone = sanitizeInput($_POST['phone'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $notes = sanitizeInput($_POST['notes'] ?? '');
        
        // التحقق من البيانات المطلوبة
        if (empty($full_name)) {
            $error_message = 'يرجى إدخال الاسم الكامل';
        } elseif (empty($university_id)) {
            $error_message = 'يرجى إدخال رقم الـ ID الجامعي';
        } elseif ($course_id <= 0) {
            $error_message = 'يرجى اختيار الكورس';
        } elseif (!empty($email) && !validateEmail($email)) {
            $error_message = 'البريد الإلكتروني غير صحيح';
        } elseif (!empty($phone) && !validatePhone($phone)) {
            $error_message = 'رقم الهاتف غير صحيح';
        } else {
            try {
                // التحقق من عدم تكرار رقم الـ ID الجامعي
                $existing_student = getDB()->fetch("SELECT id FROM students WHERE university_id = ?", [$university_id]);
                if ($existing_student) {
                    $error_message = 'رقم الـ ID الجامعي موجود مسبقاً';
                } else {
                    // بدء معاملة قاعدة البيانات
                    getDB()->beginTransaction();
                    
                    $payment_image = null;
                    $id_card_image = null;
                    
                    // رفع صورة الدفع
                    if (isset($_FILES['payment_image']) && $_FILES['payment_image']['error'] === UPLOAD_ERR_OK) {
                        try {
                            $payment_image = uploadFile($_FILES['payment_image'], ALLOWED_IMAGE_TYPES, 'uploads/payments/');
                        } catch (Exception $e) {
                            throw new Exception('خطأ في رفع صورة الدفع: ' . $e->getMessage());
                        }
                    }
                    
                    // رفع صورة بطاقة الهوية
                    if (isset($_FILES['id_card_image']) && $_FILES['id_card_image']['error'] === UPLOAD_ERR_OK) {
                        try {
                            $id_card_image = uploadFile($_FILES['id_card_image'], array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_DOCUMENT_TYPES), 'uploads/id_cards/');
                        } catch (Exception $e) {
                            // حذف صورة الدفع إذا تم رفعها
                            if ($payment_image) {
                                deleteFile($payment_image, 'uploads/payments/');
                            }
                            throw new Exception('خطأ في رفع صورة بطاقة الهوية: ' . $e->getMessage());
                        }
                    }
                    
                    // إدراج بيانات الطالب
                    $sql = "INSERT INTO students (full_name, university_id, course_id, payment_image, id_card_image, phone, email, notes, created_by) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    
                    $params = [
                        $full_name,
                        $university_id,
                        $course_id,
                        $payment_image,
                        $id_card_image,
                        $phone,
                        $email,
                        $notes,
                        $_SESSION['user_id']
                    ];
                    
                    $student_id = getDB()->insert($sql, $params);
                    
                    // تسجيل العملية
                    logActivity($_SESSION['user_id'], 'add_student', 'students', $student_id, null, [
                        'full_name' => $full_name,
                        'university_id' => $university_id,
                        'course_id' => $course_id
                    ]);
                    
                    // تأكيد المعاملة
                    getDB()->commit();
                    
                    $success_message = 'تم إضافة الطالب بنجاح';
                    
                    // إعادة تعيين النموذج
                    $full_name = $university_id = $phone = $email = $notes = '';
                    $course_id = 0;
                }
            } catch (Exception $e) {
                // إلغاء المعاملة
                getDB()->rollback();
                
                error_log("Add Student Error: " . $e->getMessage());
                $error_message = $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة طالب جديد - مركز التدريب وخدمة المجتمع</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة طالب جديد
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="view_students.php" class="btn btn-outline-secondary">
                            <i class="bi bi-list-ul me-1"></i>
                            عرض الطلاب
                        </a>
                    </div>
                </div>
                
                <!-- Alerts -->
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Student Form -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card shadow">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-person-fill me-2"></i>
                                    بيانات الطالب
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="" enctype="multipart/form-data" id="studentForm">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    
                                    <div class="row">
                                        <!-- الاسم الكامل -->
                                        <div class="col-md-6 mb-3">
                                            <label for="full_name" class="form-label">
                                                <i class="bi bi-person me-1"></i>
                                                الاسم الكامل <span class="text-danger">*</span>
                                            </label>
                                            <input type="text" 
                                                   class="form-control" 
                                                   id="full_name" 
                                                   name="full_name" 
                                                   value="<?php echo htmlspecialchars($full_name ?? ''); ?>"
                                                   required 
                                                   placeholder="أدخل الاسم الكامل">
                                        </div>
                                        
                                        <!-- رقم الـ ID الجامعي -->
                                        <div class="col-md-6 mb-3">
                                            <label for="university_id" class="form-label">
                                                <i class="bi bi-card-text me-1"></i>
                                                رقم الـ ID الجامعي <span class="text-danger">*</span>
                                            </label>
                                            <input type="text" 
                                                   class="form-control" 
                                                   id="university_id" 
                                                   name="university_id" 
                                                   value="<?php echo htmlspecialchars($university_id ?? ''); ?>"
                                                   required 
                                                   placeholder="أدخل رقم الـ ID الجامعي">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <!-- الكورس -->
                                        <div class="col-md-6 mb-3">
                                            <label for="course_id" class="form-label">
                                                <i class="bi bi-book me-1"></i>
                                                الكورس <span class="text-danger">*</span>
                                            </label>
                                            <select class="form-select" id="course_id" name="course_id" required>
                                                <option value="">اختر الكورس</option>
                                                <?php foreach ($courses as $course): ?>
                                                    <option value="<?php echo $course['id']; ?>" 
                                                            <?php echo (isset($course_id) && $course_id == $course['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($course['course_name']); ?>
                                                        (<?php echo htmlspecialchars($course['course_code']); ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        
                                        <!-- رقم الهاتف -->
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">
                                                <i class="bi bi-telephone me-1"></i>
                                                رقم الهاتف
                                            </label>
                                            <input type="tel" 
                                                   class="form-control" 
                                                   id="phone" 
                                                   name="phone" 
                                                   value="<?php echo htmlspecialchars($phone ?? ''); ?>"
                                                   placeholder="أدخل رقم الهاتف">
                                        </div>
                                    </div>
                                    
                                    <!-- البريد الإلكتروني -->
                                    <div class="mb-3">
                                        <label for="email" class="form-label">
                                            <i class="bi bi-envelope me-1"></i>
                                            البريد الإلكتروني
                                        </label>
                                        <input type="email" 
                                               class="form-control" 
                                               id="email" 
                                               name="email" 
                                               value="<?php echo htmlspecialchars($email ?? ''); ?>"
                                               placeholder="أدخل البريد الإلكتروني">
                                    </div>
                                    
                                    <div class="row">
                                        <!-- صورة الدفع -->
                                        <div class="col-md-6 mb-3">
                                            <label for="payment_image" class="form-label">
                                                <i class="bi bi-image me-1"></i>
                                                صورة الدفع
                                            </label>
                                            <input type="file" 
                                                   class="form-control" 
                                                   id="payment_image" 
                                                   name="payment_image" 
                                                   accept=".jpg,.jpeg,.png,.gif">
                                            <div class="form-text">أنواع الملفات المسموحة: JPG, PNG, GIF (حد أقصى 5 ميجابايت)</div>
                                        </div>
                                        
                                        <!-- صورة بطاقة الهوية -->
                                        <div class="col-md-6 mb-3">
                                            <label for="id_card_image" class="form-label">
                                                <i class="bi bi-card-image me-1"></i>
                                                صورة بطاقة الهوية
                                            </label>
                                            <input type="file" 
                                                   class="form-control" 
                                                   id="id_card_image" 
                                                   name="id_card_image" 
                                                   accept=".jpg,.jpeg,.png,.gif,.pdf">
                                            <div class="form-text">أنواع الملفات المسموحة: JPG, PNG, GIF, PDF (حد أقصى 5 ميجابايت)</div>
                                        </div>
                                    </div>
                                    
                                    <!-- ملاحظات -->
                                    <div class="mb-3">
                                        <label for="notes" class="form-label">
                                            <i class="bi bi-chat-text me-1"></i>
                                            ملاحظات
                                        </label>
                                        <textarea class="form-control" 
                                                  id="notes" 
                                                  name="notes" 
                                                  rows="3" 
                                                  placeholder="أدخل أي ملاحظات إضافية"><?php echo htmlspecialchars($notes ?? ''); ?></textarea>
                                    </div>
                                    
                                    <!-- أزرار الإجراءات -->
                                    <div class="d-flex justify-content-between">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-save me-2"></i>
                                            حفظ البيانات
                                        </button>
                                        <button type="reset" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-clockwise me-2"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات مساعدة -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="bi bi-info-circle me-2"></i>
                                    معلومات مهمة
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        رقم الـ ID الجامعي يجب أن يكون فريد
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        حجم الملفات المرفوعة لا يتجاوز 5 ميجابايت
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        يمكن رفع صور بصيغة JPG, PNG, GIF
                                    </li>
                                    <li>
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        بطاقة الهوية يمكن أن تكون صورة أو PDF
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/add_student.js"></script>
</body>
</html>
