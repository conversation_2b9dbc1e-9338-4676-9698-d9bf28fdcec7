<?php
/**
 * صفحة عرض الطلاب
 * View Students Page
 */

require_once 'config/database.php';
require_once 'config/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

$error_message = '';
$success_message = '';

// معاملات البحث والتصفية
$search = sanitizeInput($_GET['search'] ?? '');
$course_filter = (int)($_GET['course_filter'] ?? 0);
$payment_filter = sanitizeInput($_GET['payment_filter'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(s.full_name LIKE ? OR s.university_id LIKE ? OR s.phone LIKE ? OR s.email LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

if ($course_filter > 0) {
    $where_conditions[] = "s.course_id = ?";
    $params[] = $course_filter;
}

if (!empty($payment_filter)) {
    $where_conditions[] = "s.payment_status = ?";
    $params[] = $payment_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    // الحصول على العدد الإجمالي للطلاب
    $count_sql = "SELECT COUNT(*) as total FROM students s $where_clause";
    $total_students = getDB()->fetch($count_sql, $params)['total'];
    $total_pages = ceil($total_students / $per_page);
    
    // الحصول على بيانات الطلاب
    $sql = "SELECT s.*, c.course_name, c.course_code 
            FROM students s 
            LEFT JOIN courses c ON s.course_id = c.id 
            $where_clause 
            ORDER BY s.created_at DESC 
            LIMIT $per_page OFFSET $offset";
    
    $students = getDB()->fetchAll($sql, $params);
    
    // الحصول على قائمة الكورسات للتصفية
    $courses = getDB()->fetchAll("SELECT id, course_name FROM courses WHERE status = 'active' ORDER BY course_name");
    
} catch (Exception $e) {
    error_log("View Students Error: " . $e->getMessage());
    $error_message = "حدث خطأ في تحميل البيانات";
    $students = [];
    $courses = [];
    $total_students = 0;
    $total_pages = 0;
}

// معالجة حذف الطالب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $student_id = (int)($_POST['student_id'] ?? 0);
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'خطأ في التحقق من الأمان';
    } elseif ($student_id <= 0) {
        $error_message = 'معرف الطالب غير صحيح';
    } else {
        try {
            // الحصول على بيانات الطالب قبل الحذف
            $student = getDB()->fetch("SELECT * FROM students WHERE id = ?", [$student_id]);
            
            if (!$student) {
                $error_message = 'الطالب غير موجود';
            } else {
                // بدء معاملة
                getDB()->beginTransaction();
                
                // حذف الملفات المرفوعة
                if (!empty($student['payment_image'])) {
                    deleteFile($student['payment_image'], 'uploads/payments/');
                }
                if (!empty($student['id_card_image'])) {
                    deleteFile($student['id_card_image'], 'uploads/id_cards/');
                }
                
                // حذف الطالب من قاعدة البيانات
                $deleted = getDB()->delete("DELETE FROM students WHERE id = ?", [$student_id]);
                
                if ($deleted > 0) {
                    // تسجيل العملية
                    logActivity($_SESSION['user_id'], 'delete_student', 'students', $student_id, $student, null);
                    
                    // تأكيد المعاملة
                    getDB()->commit();
                    
                    $success_message = 'تم حذف الطالب بنجاح';
                } else {
                    getDB()->rollback();
                    $error_message = 'فشل في حذف الطالب';
                }
            }
        } catch (Exception $e) {
            getDB()->rollback();
            error_log("Delete Student Error: " . $e->getMessage());
            $error_message = 'حدث خطأ أثناء حذف الطالب';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الطلاب - مركز التدريب وخدمة المجتمع</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-people me-2"></i>
                        عرض الطلاب
                        <span class="badge bg-primary"><?php echo number_format($total_students); ?></span>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="add_student.php" class="btn btn-primary">
                            <i class="bi bi-person-plus me-1"></i>
                            إضافة طالب جديد
                        </a>
                    </div>
                </div>
                
                <!-- Alerts -->
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Search and Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="search" 
                                       name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="البحث بالاسم أو رقم ID أو الهاتف أو البريد">
                            </div>
                            <div class="col-md-3">
                                <label for="course_filter" class="form-label">الكورس</label>
                                <select class="form-select" id="course_filter" name="course_filter">
                                    <option value="">جميع الكورسات</option>
                                    <?php foreach ($courses as $course): ?>
                                        <option value="<?php echo $course['id']; ?>" 
                                                <?php echo ($course_filter == $course['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($course['course_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="payment_filter" class="form-label">حالة الدفع</label>
                                <select class="form-select" id="payment_filter" name="payment_filter">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" <?php echo ($payment_filter === 'pending') ? 'selected' : ''; ?>>معلق</option>
                                    <option value="paid" <?php echo ($payment_filter === 'paid') ? 'selected' : ''; ?>>مدفوع</option>
                                    <option value="refunded" <?php echo ($payment_filter === 'refunded') ? 'selected' : ''; ?>>مسترد</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search me-1"></i>
                                        بحث
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Students Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-table me-2"></i>
                            قائمة الطلاب
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($students)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>#</th>
                                            <th>الاسم الكامل</th>
                                            <th>رقم ID الجامعي</th>
                                            <th>الكورس</th>
                                            <th>الهاتف</th>
                                            <th>حالة الدفع</th>
                                            <th>تاريخ التسجيل</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($students as $index => $student): ?>
                                            <tr>
                                                <td><?php echo $offset + $index + 1; ?></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($student['full_name']); ?></strong>
                                                    <?php if (!empty($student['email'])): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo htmlspecialchars($student['university_id']); ?></span>
                                                </td>
                                                <td>
                                                    <?php if (!empty($student['course_name'])): ?>
                                                        <?php echo htmlspecialchars($student['course_name']); ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($student['course_code']); ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير محدد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($student['phone'])): ?>
                                                        <a href="tel:<?php echo htmlspecialchars($student['phone']); ?>" class="text-decoration-none">
                                                            <?php echo htmlspecialchars($student['phone']); ?>
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير محدد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $payment_status = $student['payment_status'];
                                                    $badge_class = '';
                                                    $status_text = '';

                                                    switch ($payment_status) {
                                                        case 'paid':
                                                            $badge_class = 'bg-success';
                                                            $status_text = 'مدفوع';
                                                            break;
                                                        case 'pending':
                                                            $badge_class = 'bg-warning';
                                                            $status_text = 'معلق';
                                                            break;
                                                        case 'refunded':
                                                            $badge_class = 'bg-info';
                                                            $status_text = 'مسترد';
                                                            break;
                                                        default:
                                                            $badge_class = 'bg-secondary';
                                                            $status_text = 'غير محدد';
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $badge_class; ?>"><?php echo $status_text; ?></span>
                                                </td>
                                                <td>
                                                    <?php echo formatDateArabic($student['created_at']); ?>
                                                    <br><small class="text-muted"><?php echo formatDate($student['created_at'], 'H:i'); ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <!-- عرض التفاصيل -->
                                                        <button type="button"
                                                                class="btn btn-sm btn-outline-info"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#studentModal<?php echo $student['id']; ?>">
                                                            <i class="bi bi-eye"></i>
                                                        </button>

                                                        <!-- تعديل -->
                                                        <a href="edit_student.php?id=<?php echo $student['id']; ?>"
                                                           class="btn btn-sm btn-outline-warning">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>

                                                        <!-- حذف -->
                                                        <button type="button"
                                                                class="btn btn-sm btn-outline-danger"
                                                                onclick="confirmDelete(<?php echo $student['id']; ?>, '<?php echo htmlspecialchars($student['full_name'], ENT_QUOTES); ?>')">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>
                                        <small class="text-muted">
                                            عرض <?php echo $offset + 1; ?> إلى <?php echo min($offset + $per_page, $total_students); ?>
                                            من أصل <?php echo number_format($total_students); ?> طالب
                                        </small>
                                    </div>
                                    <div>
                                        <?php
                                        $base_url = '?search=' . urlencode($search) .
                                                   '&course_filter=' . $course_filter .
                                                   '&payment_filter=' . urlencode($payment_filter);
                                        echo createPagination($page, $total_pages, $base_url);
                                        ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="bi bi-people display-1 text-muted"></i>
                                <h4 class="mt-3">لا توجد بيانات طلاب</h4>
                                <p class="text-muted">لم يتم العثور على أي طلاب مطابقين لمعايير البحث</p>
                                <a href="add_student.php" class="btn btn-primary">
                                    <i class="bi bi-person-plus me-2"></i>
                                    إضافة طالب جديد
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Student Details Modals -->
    <?php foreach ($students as $student): ?>
        <div class="modal fade" id="studentModal<?php echo $student['id']; ?>" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-person-circle me-2"></i>
                            تفاصيل الطالب: <?php echo htmlspecialchars($student['full_name']); ?>
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>المعلومات الأساسية</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>الاسم الكامل:</strong></td>
                                        <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>رقم ID الجامعي:</strong></td>
                                        <td><?php echo htmlspecialchars($student['university_id']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>الكورس:</strong></td>
                                        <td><?php echo htmlspecialchars($student['course_name'] ?? 'غير محدد'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>الهاتف:</strong></td>
                                        <td><?php echo htmlspecialchars($student['phone'] ?? 'غير محدد'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>البريد الإلكتروني:</strong></td>
                                        <td><?php echo htmlspecialchars($student['email'] ?? 'غير محدد'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>حالة الدفع:</strong></td>
                                        <td>
                                            <?php
                                            switch ($student['payment_status']) {
                                                case 'paid': echo '<span class="badge bg-success">مدفوع</span>'; break;
                                                case 'pending': echo '<span class="badge bg-warning">معلق</span>'; break;
                                                case 'refunded': echo '<span class="badge bg-info">مسترد</span>'; break;
                                                default: echo '<span class="badge bg-secondary">غير محدد</span>';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاريخ التسجيل:</strong></td>
                                        <td><?php echo formatDateArabic($student['created_at']); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>الملفات المرفوعة</h6>

                                <!-- صورة الدفع -->
                                <?php if (!empty($student['payment_image'])): ?>
                                    <div class="mb-3">
                                        <label class="form-label"><strong>صورة الدفع:</strong></label>
                                        <div>
                                            <a href="uploads/payments/<?php echo htmlspecialchars($student['payment_image']); ?>"
                                               target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-image me-1"></i>
                                                عرض صورة الدفع
                                            </a>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- صورة بطاقة الهوية -->
                                <?php if (!empty($student['id_card_image'])): ?>
                                    <div class="mb-3">
                                        <label class="form-label"><strong>صورة بطاقة الهوية:</strong></label>
                                        <div>
                                            <a href="uploads/id_cards/<?php echo htmlspecialchars($student['id_card_image']); ?>"
                                               target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-card-image me-1"></i>
                                                عرض بطاقة الهوية
                                            </a>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- الملاحظات -->
                                <?php if (!empty($student['notes'])): ?>
                                    <div class="mb-3">
                                        <label class="form-label"><strong>الملاحظات:</strong></label>
                                        <div class="border p-2 rounded bg-light">
                                            <?php echo nl2br(htmlspecialchars($student['notes'])); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="edit_student.php?id=<?php echo $student['id']; ?>" class="btn btn-warning">
                            <i class="bi bi-pencil me-1"></i>
                            تعديل
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                        تأكيد الحذف
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف الطالب <strong id="studentNameToDelete"></strong>؟</p>
                    <p class="text-danger">
                        <i class="bi bi-exclamation-triangle me-1"></i>
                        هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع الملفات المرفوعة.
                    </p>
                </div>
                <div class="modal-footer">
                    <form method="POST" action="" id="deleteForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="student_id" id="studentIdToDelete">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-trash me-1"></i>
                            حذف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function confirmDelete(studentId, studentName) {
            document.getElementById('studentIdToDelete').value = studentId;
            document.getElementById('studentNameToDelete').textContent = studentName;

            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        // Auto-submit search form on filter change
        document.getElementById('course_filter').addEventListener('change', function() {
            this.form.submit();
        });

        document.getElementById('payment_filter').addEventListener('change', function() {
            this.form.submit();
        });
    </script>
</body>
</html>
