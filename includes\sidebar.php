<?php
/**
 * الشريط الجانبي
 * Sidebar Navigation
 */

// تحديد الصفحة الحالية
$current_page = basename($_SERVER['PHP_SELF']);
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <!-- Dashboard -->
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'dashboard.php') ? 'active' : ''; ?>" 
                   href="dashboard.php">
                    <i class="bi bi-speedometer2 me-2"></i>
                    لوحة التحكم
                </a>
            </li>
            
            <!-- Students Section -->
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>إدارة الطلاب</span>
                </h6>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'add_student.php') ? 'active' : ''; ?>" 
                   href="add_student.php">
                    <i class="bi bi-person-plus me-2"></i>
                    إضافة طالب جديد
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'view_students.php') ? 'active' : ''; ?>" 
                   href="view_students.php">
                    <i class="bi bi-people me-2"></i>
                    عرض الطلاب
                </a>
            </li>
            
            <!-- Courses Section -->
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>إدارة الكورسات</span>
                </h6>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'manage_courses.php') ? 'active' : ''; ?>" 
                   href="manage_courses.php">
                    <i class="bi bi-book me-2"></i>
                    إدارة الكورسات
                </a>
            </li>
            
            <!-- Reports Section -->
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>التقارير</span>
                </h6>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="reports.php">
                    <i class="bi bi-graph-up me-2"></i>
                    تقارير الطلاب
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="financial_reports.php">
                    <i class="bi bi-currency-dollar me-2"></i>
                    التقارير المالية
                </a>
            </li>
            
            <!-- System Section -->
            <?php if ($_SESSION['user_role'] === 'admin'): ?>
            <li class="nav-item">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>إدارة النظام</span>
                </h6>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="manage_users.php">
                    <i class="bi bi-person-gear me-2"></i>
                    إدارة المستخدمين
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="system_logs.php">
                    <i class="bi bi-journal-text me-2"></i>
                    سجل العمليات
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="backup.php">
                    <i class="bi bi-download me-2"></i>
                    النسخ الاحتياطي
                </a>
            </li>
            <?php endif; ?>
        </ul>
        
        <!-- Quick Stats -->
        <div class="mt-4 p-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="bi bi-info-circle me-1"></i>
                        إحصائيات سريعة
                    </h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h5 mb-0">
                                <?php 
                                try {
                                    echo getDB()->fetch("SELECT COUNT(*) as count FROM students")['count'];
                                } catch (Exception $e) {
                                    echo "0";
                                }
                                ?>
                            </div>
                            <small>طالب</small>
                        </div>
                        <div class="col-6">
                            <div class="h5 mb-0">
                                <?php 
                                try {
                                    echo getDB()->fetch("SELECT COUNT(*) as count FROM courses WHERE status = 'active'")['count'];
                                } catch (Exception $e) {
                                    echo "0";
                                }
                                ?>
                            </div>
                            <small>كورس</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- User Info -->
        <div class="p-3 border-top">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                    <i class="bi bi-person-circle fs-4 text-muted"></i>
                </div>
                <div class="flex-grow-1 ms-3">
                    <div class="fw-bold small"><?php echo htmlspecialchars($_SESSION['full_name']); ?></div>
                    <div class="text-muted small"><?php echo htmlspecialchars($_SESSION['user_role']); ?></div>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #333;
    border-radius: 0.25rem;
    margin: 0 0.5rem;
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.sidebar .nav-link.active {
    background-color: #007bff;
    color: white;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 56px;
    }
}
</style>
