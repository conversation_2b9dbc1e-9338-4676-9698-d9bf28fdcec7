<?php
/**
 * الصفحة الرئيسية للوحة التحكم
 * Dashboard Main Page
 */

require_once 'config/database.php';
require_once 'config/functions.php';

// التحقق من تسجيل الدخول
requireLogin();

// الحصول على الإحصائيات
try {
    // عدد الطلاب
    $total_students = getDB()->fetch("SELECT COUNT(*) as count FROM students")['count'];
    $new_students_today = getDB()->fetch("SELECT COUNT(*) as count FROM students WHERE DATE(created_at) = CURDATE()")['count'];
    
    // عدد الكورسات
    $total_courses = getDB()->fetch("SELECT COUNT(*) as count FROM courses WHERE status = 'active'")['count'];
    $completed_courses = getDB()->fetch("SELECT COUNT(*) as count FROM courses WHERE status = 'completed'")['count'];
    
    // الطلاب حسب حالة الدفع
    $paid_students = getDB()->fetch("SELECT COUNT(*) as count FROM students WHERE payment_status = 'paid'")['count'];
    $pending_payments = getDB()->fetch("SELECT COUNT(*) as count FROM students WHERE payment_status = 'pending'")['count'];
    
    // أحدث الطلاب المسجلين
    $recent_students = getDB()->fetchAll("
        SELECT s.*, c.course_name 
        FROM students s 
        LEFT JOIN courses c ON s.course_id = c.id 
        ORDER BY s.created_at DESC 
        LIMIT 5
    ");
    
    // الكورسات الأكثر شعبية
    $popular_courses = getDB()->fetchAll("
        SELECT c.course_name, COUNT(s.id) as student_count 
        FROM courses c 
        LEFT JOIN students s ON c.id = s.course_id 
        WHERE c.status = 'active'
        GROUP BY c.id, c.course_name 
        ORDER BY student_count DESC 
        LIMIT 5
    ");
    
} catch (Exception $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    $error_message = "حدث خطأ في تحميل البيانات";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - مركز التدريب وخدمة المجتمع</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-speedometer2 me-2"></i>
                        لوحة التحكم
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-download me-1"></i>
                                تصدير التقرير
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Alerts -->
                <?php showAlert(); ?>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            إجمالي الطلاب
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($total_students); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-people-fill fa-2x text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            طلاب جدد اليوم
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($new_students_today); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-person-plus-fill fa-2x text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            الكورسات النشطة
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($total_courses); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-book-fill fa-2x text-info"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-right-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            مدفوعات معلقة
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo number_format($pending_payments); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-clock-fill fa-2x text-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts and Tables Row -->
                <div class="row">
                    <!-- Recent Students -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="bi bi-person-lines-fill me-2"></i>
                                    أحدث الطلاب المسجلين
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recent_students)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>الاسم</th>
                                                    <th>الكورس</th>
                                                    <th>تاريخ التسجيل</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recent_students as $student): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($student['course_name'] ?? 'غير محدد'); ?></td>
                                                        <td><?php echo formatDateArabic($student['created_at']); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-center">
                                        <a href="view_students.php" class="btn btn-primary btn-sm">
                                            عرض جميع الطلاب
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <p class="text-center text-muted">لا توجد بيانات طلاب</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Popular Courses -->
                    <div class="col-lg-6 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="bi bi-graph-up me-2"></i>
                                    الكورسات الأكثر شعبية
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($popular_courses)): ?>
                                    <?php foreach ($popular_courses as $course): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-0"><?php echo htmlspecialchars($course['course_name']); ?></h6>
                                            </div>
                                            <div>
                                                <span class="badge bg-primary rounded-pill">
                                                    <?php echo $course['student_count']; ?> طالب
                                                </span>
                                            </div>
                                        </div>
                                        <div class="progress mb-3" style="height: 6px;">
                                            <div class="progress-bar" 
                                                 style="width: <?php echo $total_students > 0 ? ($course['student_count'] / $total_students * 100) : 0; ?>%">
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    <div class="text-center">
                                        <a href="manage_courses.php" class="btn btn-primary btn-sm">
                                            إدارة الكورسات
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <p class="text-center text-muted">لا توجد بيانات كورسات</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
