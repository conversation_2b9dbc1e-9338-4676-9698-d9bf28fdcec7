# نظام إدارة مركز التدريب وخدمة المجتمع

نظام لوحة تحكم شامل لإدارة مركز التدريب وخدمة المجتمع، مطور باستخدام PHP + MySQL + Bootstrap مع دعم كامل للغة العربية.

## المميزات

### إدارة الطلاب
- ✅ إضافة طالب جديد مع رفع الملفات (صورة الدفع وبطاقة الهوية)
- ✅ عرض قائمة الطلاب مع إمكانية البحث والتصفية
- ✅ تعديل وحذف بيانات الطلاب
- ✅ عرض تفاصيل الطالب في نافذة منبثقة

### إدارة الكورسات
- ✅ إضافة وتعديل وحذف الكورسات
- ✅ عرض إحصائيات الطلاب لكل كورس
- ✅ إدارة حالة الكورس (نشط/غير نشط/مكتمل)

### الأمان
- ✅ تسجيل دخول آمن مع تشفير كلمات المرور
- ✅ حماية من CSRF attacks
- ✅ تسجيل جميع العمليات في قاعدة البيانات
- ✅ حماية الملفات المرفوعة

### التصميم
- ✅ واجهة احترافية متجاوبة باستخدام Bootstrap 5
- ✅ دعم كامل للغة العربية (RTL)
- ✅ أيقونات Bootstrap Icons
- ✅ تأثيرات CSS حديثة

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx مع mod_rewrite
- مساحة تخزين للملفات المرفوعة

## التثبيت

### 1. تحضير البيئة

```bash
# نسخ الملفات إلى مجلد الخادم
cp -r * /path/to/your/webserver/
```

### 2. إعداد قاعدة البيانات

1. إنشاء قاعدة بيانات جديدة:
```sql
CREATE DATABASE training_center CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. استيراد هيكل قاعدة البيانات:
```bash
mysql -u username -p training_center < database.sql
```

3. تعديل إعدادات الاتصال في `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'training_center');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. إعداد الصلاحيات

```bash
# إعطاء صلاحيات الكتابة لمجلد الملفات المرفوعة
chmod 755 uploads/
chmod 755 uploads/payments/
chmod 755 uploads/id_cards/
```

### 4. الوصول للنظام

- الرابط: `http://your-domain.com/`
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

**⚠️ مهم: يرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول**

## هيكل المشروع

```
├── config/
│   ├── database.php      # إعدادات قاعدة البيانات
│   └── functions.php     # الدوال المساعدة
├── includes/
│   ├── navbar.php        # شريط التنقل العلوي
│   └── sidebar.php       # الشريط الجانبي
├── assets/
│   ├── css/
│   │   └── style.css     # ملف التنسيق الرئيسي
│   └── js/
│       ├── dashboard.js  # JavaScript للوحة التحكم
│       └── add_student.js # JavaScript لإضافة الطلاب
├── uploads/
│   ├── payments/         # صور الدفع
│   └── id_cards/         # صور بطاقات الهوية
├── index.php             # الصفحة الرئيسية
├── login.php             # صفحة تسجيل الدخول
├── logout.php            # تسجيل الخروج
├── dashboard.php         # لوحة التحكم
├── add_student.php       # إضافة طالب جديد
├── view_students.php     # عرض الطلاب
├── manage_courses.php    # إدارة الكورسات
└── database.sql          # هيكل قاعدة البيانات
```

## الاستخدام

### إضافة طالب جديد
1. انتقل إلى "إضافة طالب جديد"
2. املأ البيانات المطلوبة
3. ارفع صورة الدفع وبطاقة الهوية
4. اختر الكورس من القائمة المنسدلة
5. انقر "حفظ البيانات"

### إدارة الكورسات
1. انتقل إلى "إدارة الكورسات"
2. انقر "إضافة كورس جديد"
3. املأ تفاصيل الكورس
4. حدد السعر وعدد الساعات
5. انقر "حفظ"

### البحث عن الطلاب
1. انتقل إلى "عرض الطلاب"
2. استخدم مربع البحث للبحث بالاسم أو رقم ID
3. استخدم المرشحات للتصفية حسب الكورس أو حالة الدفع

## الأمان

### كلمات المرور
- يتم تشفير كلمات المرور باستخدام `password_hash()`
- يُنصح بتغيير كلمة المرور الافتراضية

### الملفات المرفوعة
- يُسمح فقط بأنواع ملفات محددة (JPG, PNG, GIF, PDF)
- الحد الأقصى لحجم الملف: 5 ميجابايت
- حماية من تنفيذ ملفات PHP في مجلد الملفات

### تسجيل العمليات
- جميع العمليات المهمة يتم تسجيلها في جدول `activity_log`
- تتضمن: إضافة/تعديل/حذف الطلاب والكورسات

## التخصيص

### تغيير الألوان
عدّل المتغيرات في `assets/css/style.css`:
```css
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
}
```

### إضافة حقول جديدة
1. عدّل هيكل قاعدة البيانات
2. أضف الحقول في النماذج
3. حدّث دوال الإدراج والتحديث

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- تحقق من ملفات السجل في `/var/log/apache2/error.log`
- تأكد من صلاحيات الملفات والمجلدات
- تحقق من إعدادات قاعدة البيانات

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**تم تطوير هذا النظام خصيصاً لمراكز التدريب وخدمة المجتمع مع مراعاة المتطلبات المحلية والدعم الكامل للغة العربية.**
