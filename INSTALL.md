# تعليمات التثبيت السريع

## حل مشكلة "خطأ في الاتصال بقاعدة البيانات"

### الخطوة 1: التحقق من تشغيل الخادم

**إذا كنت تستخدم XAMPP:**
1. افتح لوحة تحكم XAMPP
2. تأكد من تشغيل Apache و MySQL (يجب أن يظهران باللون الأخضر)
3. إذا لم يعملا، انقر على "Start" بجانب كل منهما

**إذا كنت تستخدم WAMP:**
1. تأكد من أن أيقونة WAMP خضراء في شريط المهام
2. إذا كانت حمراء أو برتقالية، انقر عليها واختر "Start All Services"

### الخطوة 2: اختبار الاتصال

1. افتح المتصفح واذهب إلى: `http://localhost/taou/test_connection.php`
2. سيظهر لك تقرير مفصل عن حالة الاتصال

### الخطوة 3: إعداد النظام تلقائياً

1. اذهب إلى: `http://localhost/taou/setup.php`
2. سيقوم الملف بإنشاء قاعدة البيانات والجداول تلقائياً

### الخطوة 4: تعديل إعدادات قاعدة البيانات (إذا لزم الأمر)

إذا كانت إعدادات قاعدة البيانات مختلفة، عدّل الملف `config/database.php`:

```php
define('DB_HOST', 'localhost');     // عنوان الخادم
define('DB_NAME', 'training_center'); // اسم قاعدة البيانات
define('DB_USER', 'root');          // اسم المستخدم
define('DB_PASS', '');              // كلمة المرور (فارغة في XAMPP)
```

### أخطاء شائعة وحلولها

#### خطأ: "SQLSTATE[HY000] [2002]"
**السبب:** خادم MySQL غير متاح
**الحل:**
- تأكد من تشغيل MySQL في XAMPP/WAMP
- تحقق من أن MySQL يعمل على المنفذ 3306

#### خطأ: "SQLSTATE[28000] [1045]"
**السبب:** خطأ في اسم المستخدم أو كلمة المرور
**الحل:**
- في XAMPP الافتراضي: المستخدم `root` وكلمة المرور فارغة
- تحقق من إعدادات قاعدة البيانات

#### خطأ: "SQLSTATE[HY000] [1049]"
**السبب:** قاعدة البيانات غير موجودة
**الحل:**
- شغّل `setup.php` لإنشاء قاعدة البيانات تلقائياً

### التحقق من نجاح التثبيت

بعد حل المشكلة:
1. اذهب إلى: `http://localhost/taou/`
2. يجب أن تظهر صفحة تسجيل الدخول
3. استخدم:
   - **اسم المستخدم:** admin
   - **كلمة المرور:** admin123

### إعدادات XAMPP المطلوبة

تأكد من أن XAMPP يحتوي على:
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache مع mod_rewrite

### مجلدات مطلوبة

تأكد من وجود هذه المجلدات مع الصلاحيات المناسبة:
```
uploads/
uploads/payments/
uploads/id_cards/
```

### اختبار سريع

```bash
# في مجلد المشروع
php -v                    # تحقق من إصدار PHP
mysql --version          # تحقق من إصدار MySQL
```

### الحصول على المساعدة

إذا استمرت المشكلة:
1. شغّل `test_connection.php` وانسخ رسالة الخطأ
2. تحقق من ملفات السجل في XAMPP
3. تأكد من أن جميع الملفات في المكان الصحيح

---

**ملاحظة:** هذا النظام مصمم للعمل مع XAMPP الافتراضي بدون تعديلات إضافية.
