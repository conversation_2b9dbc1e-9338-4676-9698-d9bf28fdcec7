/**
 * ملف JavaScript لصفحة إضافة الطلاب
 * Add Student JavaScript File
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة النموذج
    initializeForm();
    
    // تهيئة رفع الملفات
    initializeFileUpload();
    
    // تهيئة التحقق من صحة البيانات
    initializeValidation();
    
    // تهيئة الاختصارات
    initializeShortcuts();
});

/**
 * تهيئة النموذج
 */
function initializeForm() {
    const form = document.getElementById('studentForm');
    if (!form) return;
    
    // التركيز على أول حقل
    const firstInput = form.querySelector('input[type="text"]');
    if (firstInput) {
        firstInput.focus();
    }
    
    // تنسيق رقم الهاتف تلقائياً
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', formatPhoneNumber);
    }
    
    // تحويل النص إلى أحرف كبيرة لرقم ID
    const universityIdInput = document.getElementById('university_id');
    if (universityIdInput) {
        universityIdInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }
    
    // معاينة البيانات قبل الإرسال
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return false;
        }
        
        showLoadingState();
    });
}

/**
 * تنسيق رقم الهاتف
 */
function formatPhoneNumber(e) {
    let value = e.target.value.replace(/\D/g, '');
    
    if (value.startsWith('966')) {
        value = value.substring(3);
    }
    
    if (value.startsWith('0')) {
        value = value.substring(1);
    }
    
    if (value.length > 0) {
        if (value.length <= 3) {
            value = value;
        } else if (value.length <= 6) {
            value = value.substring(0, 3) + '-' + value.substring(3);
        } else {
            value = value.substring(0, 3) + '-' + value.substring(3, 6) + '-' + value.substring(6, 10);
        }
    }
    
    e.target.value = value;
}

/**
 * تهيئة رفع الملفات
 */
function initializeFileUpload() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        // إنشاء منطقة السحب والإفلات
        createDropZone(input);
        
        // معاينة الملفات
        input.addEventListener('change', function() {
            previewFile(this);
        });
    });
}

/**
 * إنشاء منطقة السحب والإفلات
 */
function createDropZone(input) {
    const wrapper = document.createElement('div');
    wrapper.className = 'file-upload-wrapper position-relative';
    
    const dropZone = document.createElement('div');
    dropZone.className = 'file-upload-area border-2 border-dashed rounded p-4 text-center';
    dropZone.innerHTML = `
        <i class="bi bi-cloud-upload icon-lg text-muted mb-2"></i>
        <p class="mb-2">اسحب الملف هنا أو انقر للاختيار</p>
        <small class="text-muted">الحد الأقصى: 5 ميجابايت</small>
    `;
    
    // إدراج منطقة السحب قبل input
    input.parentNode.insertBefore(wrapper, input);
    wrapper.appendChild(dropZone);
    wrapper.appendChild(input);
    
    // إخفاء input الأصلي
    input.style.display = 'none';
    
    // أحداث السحب والإفلات
    dropZone.addEventListener('click', () => input.click());
    
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            input.files = files;
            previewFile(input);
        }
    });
}

/**
 * معاينة الملف المرفوع
 */
function previewFile(input) {
    const file = input.files[0];
    if (!file) return;
    
    const wrapper = input.closest('.file-upload-wrapper');
    const dropZone = wrapper.querySelector('.file-upload-area');
    
    // التحقق من حجم الملف
    if (file.size > 5 * 1024 * 1024) {
        showAlert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت', 'error');
        input.value = '';
        return;
    }
    
    // التحقق من نوع الملف
    const allowedTypes = input.accept.split(',').map(type => type.trim());
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
        showAlert('نوع الملف غير مسموح', 'error');
        input.value = '';
        return;
    }
    
    // عرض معلومات الملف
    dropZone.innerHTML = `
        <i class="bi bi-file-earmark-check icon-lg text-success mb-2"></i>
        <p class="mb-1 fw-bold">${file.name}</p>
        <small class="text-muted">${formatFileSize(file.size)}</small>
        <div class="mt-2">
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(this)">
                <i class="bi bi-trash me-1"></i>إزالة
            </button>
        </div>
    `;
    
    // معاينة الصورة إذا كانت صورة
    if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = document.createElement('img');
            img.src = e.target.result;
            img.className = 'img-thumbnail mt-2';
            img.style.maxWidth = '150px';
            img.style.maxHeight = '150px';
            dropZone.appendChild(img);
        };
        reader.readAsDataURL(file);
    }
}

/**
 * إزالة الملف
 */
function removeFile(button) {
    const wrapper = button.closest('.file-upload-wrapper');
    const input = wrapper.querySelector('input[type="file"]');
    const dropZone = wrapper.querySelector('.file-upload-area');
    
    input.value = '';
    
    dropZone.innerHTML = `
        <i class="bi bi-cloud-upload icon-lg text-muted mb-2"></i>
        <p class="mb-2">اسحب الملف هنا أو انقر للاختيار</p>
        <small class="text-muted">الحد الأقصى: 5 ميجابايت</small>
    `;
}

/**
 * تنسيق حجم الملف
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';
    
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * تهيئة التحقق من صحة البيانات
 */
function initializeValidation() {
    const form = document.getElementById('studentForm');
    if (!form) return;
    
    // التحقق من الحقول المطلوبة
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateField(this);
        });
        
        field.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
    
    // التحقق من البريد الإلكتروني
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('blur', function() {
            if (this.value && !isValidEmail(this.value)) {
                showFieldError(this, 'البريد الإلكتروني غير صحيح');
            }
        });
    }
    
    // التحقق من رقم الهاتف
    const phoneField = document.getElementById('phone');
    if (phoneField) {
        phoneField.addEventListener('blur', function() {
            if (this.value && !isValidPhone(this.value)) {
                showFieldError(this, 'رقم الهاتف غير صحيح');
            }
        });
    }
}

/**
 * التحقق من صحة النموذج
 */
function validateForm() {
    const form = document.getElementById('studentForm');
    let isValid = true;
    
    // التحقق من الحقول المطلوبة
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    // التحقق من البريد الإلكتروني
    const emailField = document.getElementById('email');
    if (emailField && emailField.value && !isValidEmail(emailField.value)) {
        showFieldError(emailField, 'البريد الإلكتروني غير صحيح');
        isValid = false;
    }
    
    // التحقق من رقم الهاتف
    const phoneField = document.getElementById('phone');
    if (phoneField && phoneField.value && !isValidPhone(phoneField.value)) {
        showFieldError(phoneField, 'رقم الهاتف غير صحيح');
        isValid = false;
    }
    
    return isValid;
}

/**
 * التحقق من صحة حقل واحد
 */
function validateField(field) {
    if (field.hasAttribute('required') && !field.value.trim()) {
        showFieldError(field, 'هذا الحقل مطلوب');
        return false;
    }
    
    clearFieldError(field);
    return true;
}

/**
 * عرض خطأ في الحقل
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('is-invalid');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * إزالة خطأ الحقل
 */
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * التحقق من صحة رقم الهاتف
 */
function isValidPhone(phone) {
    const phoneRegex = /^[0-9+\-\s()]{10,20}$/;
    return phoneRegex.test(phone);
}

/**
 * عرض حالة التحميل
 */
function showLoadingState() {
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
    }
}

/**
 * عرض رسالة تنبيه
 */
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('main');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}

/**
 * تهيئة الاختصارات
 */
function initializeShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + S = حفظ
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            document.getElementById('studentForm').submit();
        }
        
        // Ctrl + R = إعادة تعيين
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            if (confirm('هل تريد إعادة تعيين النموذج؟')) {
                document.getElementById('studentForm').reset();
            }
        }
    });
}

// تصدير الدوال للاستخدام العام
window.studentFormUtils = {
    validateForm,
    showAlert,
    formatFileSize,
    removeFile
};
