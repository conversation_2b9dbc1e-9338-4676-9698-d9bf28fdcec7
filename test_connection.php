<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * Database Connection Test
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

// إعدادات قاعدة البيانات
$configs = [
    'host' => 'localhost',
    'dbname' => 'training_center',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4'
];

echo "<h3>الإعدادات المستخدمة:</h3>";
echo "<ul>";
foreach ($configs as $key => $value) {
    if ($key === 'password') {
        $value = empty($value) ? '(فارغة)' : '***';
    }
    echo "<li><strong>$key:</strong> $value</li>";
}
echo "</ul>";

try {
    echo "<h3>خطوات الاختبار:</h3>";
    
    // 1. اختبار الاتصال بـ MySQL بدون قاعدة بيانات
    echo "<p>1. اختبار الاتصال بخادم MySQL...</p>";
    $dsn = "mysql:host={$configs['host']};charset={$configs['charset']}";
    $pdo = new PDO($dsn, $configs['username'], $configs['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    echo "<p style='color: green;'>✓ تم الاتصال بخادم MySQL بنجاح!</p>";
    
    // 2. التحقق من وجود قاعدة البيانات
    echo "<p>2. التحقق من وجود قاعدة البيانات...</p>";
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$configs['dbname']}'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ قاعدة البيانات '{$configs['dbname']}' موجودة!</p>";
        
        // 3. الاتصال بقاعدة البيانات المحددة
        echo "<p>3. الاتصال بقاعدة البيانات...</p>";
        $dsn = "mysql:host={$configs['host']};dbname={$configs['dbname']};charset={$configs['charset']}";
        $pdo = new PDO($dsn, $configs['username'], $configs['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح!</p>";
        
        // 4. التحقق من الجداول
        echo "<p>4. التحقق من الجداول...</p>";
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (count($tables) > 0) {
            echo "<p style='color: green;'>✓ تم العثور على " . count($tables) . " جدول:</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>$table</li>";
            }
            echo "</ul>";
            
            // 5. اختبار استعلام بسيط
            echo "<p>5. اختبار استعلام بسيط...</p>";
            if (in_array('users', $tables)) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
                $count = $stmt->fetch()['count'];
                echo "<p style='color: green;'>✓ عدد المستخدمين في النظام: $count</p>";
            }
            
            if (in_array('courses', $tables)) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM courses");
                $count = $stmt->fetch()['count'];
                echo "<p style='color: green;'>✓ عدد الكورسات في النظام: $count</p>";
            }
            
            if (in_array('students', $tables)) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM students");
                $count = $stmt->fetch()['count'];
                echo "<p style='color: green;'>✓ عدد الطلاب في النظام: $count</p>";
            }
            
        } else {
            echo "<p style='color: orange;'>⚠ قاعدة البيانات فارغة (لا توجد جداول)</p>";
            echo "<p><a href='setup.php'>انقر هنا لإعداد النظام</a></p>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠ قاعدة البيانات '{$configs['dbname']}' غير موجودة</p>";
        echo "<p><a href='setup.php'>انقر هنا لإعداد النظام</a></p>";
    }
    
    echo "<h3 style='color: green;'>✓ اختبار الاتصال مكتمل بنجاح!</h3>";
    echo "<p><a href='index.php'>الانتقال إلى النظام</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>✗ خطأ في الاتصال بقاعدة البيانات</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    
    echo "<h4>الحلول المقترحة:</h4>";
    echo "<ul>";
    echo "<li><strong>إذا كنت تستخدم XAMPP:</strong>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل Apache و MySQL من لوحة تحكم XAMPP</li>";
    echo "<li>تحقق من أن MySQL يعمل على المنفذ 3306</li>";
    echo "</ul></li>";
    
    echo "<li><strong>إذا كنت تستخدم خادم مختلف:</strong>";
    echo "<ul>";
    echo "<li>تحقق من إعدادات الاتصال في config/database.php</li>";
    echo "<li>تأكد من صحة اسم المستخدم وكلمة المرور</li>";
    echo "<li>تحقق من أن خادم MySQL يعمل</li>";
    echo "</ul></li>";
    
    echo "<li><strong>أخطاء شائعة:</strong>";
    echo "<ul>";
    echo "<li>SQLSTATE[HY000] [2002] - خادم MySQL غير متاح</li>";
    echo "<li>SQLSTATE[28000] [1045] - خطأ في اسم المستخدم أو كلمة المرور</li>";
    echo "<li>SQLSTATE[HY000] [1049] - قاعدة البيانات غير موجودة</li>";
    echo "</ul></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>✗ خطأ عام</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h4>معلومات النظام:</h4>";
echo "<ul>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>امتدادات PDO المتاحة:</strong> " . implode(', ', PDO::getAvailableDrivers()) . "</li>";
echo "<li><strong>المجلد الحالي:</strong> " . __DIR__ . "</li>";
echo "<li><strong>الوقت:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "</ul>";
?>
