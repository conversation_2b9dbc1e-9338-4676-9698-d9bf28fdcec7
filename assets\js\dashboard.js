/**
 * ملف JavaScript للوحة التحكم
 * Dashboard JavaScript File
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التوقيتات
    initializeTimers();
    
    // تهيئة الرسوم البيانية
    initializeCharts();
    
    // تهيئة التنبيهات
    initializeNotifications();
    
    // تهيئة الاختصارات
    initializeShortcuts();
});

/**
 * تهيئة التوقيتات والتحديث التلقائي
 */
function initializeTimers() {
    // تحديث الوقت كل ثانية
    setInterval(updateCurrentTime, 1000);
    
    // تحديث الإحصائيات كل 5 دقائق
    setInterval(refreshStats, 300000);
    
    updateCurrentTime();
}

/**
 * تحديث الوقت الحالي
 */
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    const dateString = now.toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    // تحديث عنصر الوقت إذا كان موجوداً
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
    
    const dateElement = document.getElementById('current-date');
    if (dateElement) {
        dateElement.textContent = dateString;
    }
}

/**
 * تحديث الإحصائيات
 */
function refreshStats() {
    fetch('api/get_stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatCards(data.stats);
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

/**
 * تحديث بطاقات الإحصائيات
 */
function updateStatCards(stats) {
    const elements = {
        'total-students': stats.total_students,
        'new-students': stats.new_students_today,
        'active-courses': stats.total_courses,
        'pending-payments': stats.pending_payments
    };
    
    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            animateNumber(element, parseInt(element.textContent.replace(/,/g, '')), elements[id]);
        }
    });
}

/**
 * تحريك الأرقام
 */
function animateNumber(element, start, end) {
    const duration = 1000;
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current.toLocaleString('ar-SA');
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

/**
 * تهيئة الرسوم البيانية
 */
function initializeCharts() {
    // رسم بياني للطلاب الجدد
    createStudentsChart();
    
    // رسم بياني للكورسات
    createCoursesChart();
}

/**
 * إنشاء رسم بياني للطلاب
 */
function createStudentsChart() {
    const canvas = document.getElementById('studentsChart');
    if (!canvas) return;
    
    // بيانات تجريبية - يمكن استبدالها ببيانات حقيقية من API
    const data = {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'الطلاب الجدد',
            data: [12, 19, 15, 25, 22, 30],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }]
    };
    
    // يمكن استخدام Chart.js هنا إذا تم تضمينه
    console.log('رسم بياني للطلاب:', data);
}

/**
 * إنشاء رسم بياني للكورسات
 */
function createCoursesChart() {
    const canvas = document.getElementById('coursesChart');
    if (!canvas) return;
    
    // بيانات تجريبية
    const data = {
        labels: ['نشط', 'مكتمل', 'معلق'],
        datasets: [{
            data: [65, 25, 10],
            backgroundColor: ['#28a745', '#17a2b8', '#ffc107']
        }]
    };
    
    console.log('رسم بياني للكورسات:', data);
}

/**
 * تهيئة التنبيهات
 */
function initializeNotifications() {
    // التحقق من التنبيهات الجديدة كل دقيقة
    setInterval(checkNotifications, 60000);
    
    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            fadeOut(alert);
        }, 5000);
    });
}

/**
 * التحقق من التنبيهات الجديدة
 */
function checkNotifications() {
    fetch('api/get_notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.notifications.length > 0) {
                updateNotificationBadge(data.notifications.length);
                showNewNotifications(data.notifications);
            }
        })
        .catch(error => {
            console.error('خطأ في التحقق من التنبيهات:', error);
        });
}

/**
 * تحديث شارة التنبيهات
 */
function updateNotificationBadge(count) {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'inline' : 'none';
    }
}

/**
 * عرض التنبيهات الجديدة
 */
function showNewNotifications(notifications) {
    notifications.forEach(notification => {
        showToast(notification.message, notification.type);
    });
}

/**
 * عرض رسالة منبثقة
 */
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    const container = document.getElementById('toast-container') || createToastContainer();
    container.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // إزالة التوست بعد إخفائه
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

/**
 * إنشاء حاوية التوست
 */
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}

/**
 * تهيئة الاختصارات
 */
function initializeShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + Alt + D = لوحة التحكم
        if (e.ctrlKey && e.altKey && e.key === 'd') {
            e.preventDefault();
            window.location.href = 'dashboard.php';
        }
        
        // Ctrl + Alt + S = إضافة طالب
        if (e.ctrlKey && e.altKey && e.key === 's') {
            e.preventDefault();
            window.location.href = 'add_student.php';
        }
        
        // Ctrl + Alt + C = إدارة الكورسات
        if (e.ctrlKey && e.altKey && e.key === 'c') {
            e.preventDefault();
            window.location.href = 'manage_courses.php';
        }
        
        // Ctrl + Alt + V = عرض الطلاب
        if (e.ctrlKey && e.altKey && e.key === 'v') {
            e.preventDefault();
            window.location.href = 'view_students.php';
        }
    });
}

/**
 * تأثير الاختفاء التدريجي
 */
function fadeOut(element) {
    element.style.transition = 'opacity 0.5s';
    element.style.opacity = '0';
    
    setTimeout(() => {
        element.remove();
    }, 500);
}

/**
 * تأثير الظهور التدريجي
 */
function fadeIn(element) {
    element.style.opacity = '0';
    element.style.transition = 'opacity 0.5s';
    
    setTimeout(() => {
        element.style.opacity = '1';
    }, 10);
}

/**
 * تحديث الصفحة بسلاسة
 */
function smoothRefresh() {
    document.body.style.transition = 'opacity 0.3s';
    document.body.style.opacity = '0';
    
    setTimeout(() => {
        window.location.reload();
    }, 300);
}

/**
 * تصدير البيانات
 */
function exportData(type) {
    const url = `api/export.php?type=${type}`;
    
    // إنشاء رابط تحميل مؤقت
    const link = document.createElement('a');
    link.href = url;
    link.download = `${type}_${new Date().toISOString().split('T')[0]}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showToast('تم بدء تحميل الملف', 'success');
}

/**
 * طباعة التقرير
 */
function printReport() {
    window.print();
}

/**
 * البحث السريع
 */
function quickSearch(query) {
    if (query.length < 2) return;
    
    fetch(`api/quick_search.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data.results);
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
        });
}

/**
 * عرض نتائج البحث
 */
function displaySearchResults(results) {
    const container = document.getElementById('search-results');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (results.length === 0) {
        container.innerHTML = '<div class="text-muted">لا توجد نتائج</div>';
        return;
    }
    
    results.forEach(result => {
        const item = document.createElement('div');
        item.className = 'search-result-item p-2 border-bottom';
        item.innerHTML = `
            <div class="fw-bold">${result.title}</div>
            <div class="text-muted small">${result.description}</div>
        `;
        item.addEventListener('click', () => {
            window.location.href = result.url;
        });
        container.appendChild(item);
    });
}

// تصدير الدوال للاستخدام العام
window.dashboardUtils = {
    refreshStats,
    exportData,
    printReport,
    quickSearch,
    showToast,
    smoothRefresh
};
