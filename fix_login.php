<?php
/**
 * ملف إصلاح مشاكل تسجيل الدخول
 * Login Fix File
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح مشاكل تسجيل الدخول</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>
    <div class='container mt-5'>
        <div class='card'>
            <div class='card-header bg-success text-white'>
                <h4>إصلاح مشاكل تسجيل الدخول</h4>
            </div>
            <div class='card-body'>";

try {
    // إعدادات قاعدة البيانات
    $db_host = 'localhost';
    $db_user = 'root';
    $db_pass = '';
    $db_name = 'training_center';
    $db_charset = 'utf8mb4';

    echo "<h5>جاري إصلاح المشاكل...</h5>";
    
    // 1. إنشاء قاعدة البيانات إذا لم تكن موجودة
    echo "<p>1. التحقق من قاعدة البيانات...</p>";
    $dsn = "mysql:host=$db_host;charset=$db_charset";
    $pdo = new PDO($dsn, $db_user, $db_pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // التحقق من وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE '$db_name'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("CREATE DATABASE `$db_name` CHARACTER SET $db_charset COLLATE {$db_charset}_unicode_ci");
        echo "<p class='text-success'>✓ تم إنشاء قاعدة البيانات</p>";
    } else {
        echo "<p class='text-info'>✓ قاعدة البيانات موجودة</p>";
    }
    
    // الاتصال بقاعدة البيانات
    $dsn = "mysql:host=$db_host;dbname=$db_name;charset=$db_charset";
    $pdo = new PDO($dsn, $db_user, $db_pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // 2. إنشاء جدول المستخدمين
    echo "<p>2. إنشاء جدول المستخدمين...</p>";
    $create_users_table = "
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        role ENUM('admin', 'staff') DEFAULT 'staff',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE
    )";
    $pdo->exec($create_users_table);
    echo "<p class='text-success'>✓ تم إنشاء جدول المستخدمين</p>";
    
    // 3. إنشاء جدول الكورسات
    echo "<p>3. إنشاء جدول الكورسات...</p>";
    $create_courses_table = "
    CREATE TABLE IF NOT EXISTS courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_name VARCHAR(100) NOT NULL,
        course_code VARCHAR(20) UNIQUE,
        description TEXT,
        duration_hours INT,
        price DECIMAL(10,2),
        instructor_name VARCHAR(100),
        max_students INT DEFAULT 30,
        start_date DATE,
        end_date DATE,
        status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($create_courses_table);
    echo "<p class='text-success'>✓ تم إنشاء جدول الكورسات</p>";
    
    // 4. إنشاء جدول الطلاب
    echo "<p>4. إنشاء جدول الطلاب...</p>";
    $create_students_table = "
    CREATE TABLE IF NOT EXISTS students (
        id INT AUTO_INCREMENT PRIMARY KEY,
        full_name VARCHAR(100) NOT NULL,
        university_id VARCHAR(50) NOT NULL UNIQUE,
        course_id INT,
        payment_image VARCHAR(255),
        id_card_image VARCHAR(255),
        phone VARCHAR(20),
        email VARCHAR(100),
        registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
        attendance_status ENUM('enrolled', 'completed', 'dropped') DEFAULT 'enrolled',
        notes TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_university_id (university_id),
        INDEX idx_course_id (course_id),
        INDEX idx_full_name (full_name)
    )";
    $pdo->exec($create_students_table);
    echo "<p class='text-success'>✓ تم إنشاء جدول الطلاب</p>";
    
    // 5. إنشاء جدول سجل العمليات
    echo "<p>5. إنشاء جدول سجل العمليات...</p>";
    $create_activity_log_table = "
    CREATE TABLE IF NOT EXISTS activity_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(50) NOT NULL,
        table_name VARCHAR(50),
        record_id INT,
        old_values JSON,
        new_values JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_created_at (created_at)
    )";
    $pdo->exec($create_activity_log_table);
    echo "<p class='text-success'>✓ تم إنشاء جدول سجل العمليات</p>";
    
    // 6. إنشاء المستخدم الافتراضي
    echo "<p>6. إنشاء المستخدم الافتراضي...</p>";
    
    // التحقق من وجود المستخدم admin
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin_exists = $stmt->fetch()['count'] > 0;
    
    if (!$admin_exists) {
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, full_name, email, role, is_active) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin', $password_hash, 'مدير النظام', '<EMAIL>', 'admin', 1]);
        echo "<p class='text-success'>✓ تم إنشاء المستخدم admin</p>";
    } else {
        // إعادة تعيين كلمة المرور للتأكد
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ?, is_active = 1 WHERE username = 'admin'");
        $stmt->execute([$password_hash]);
        echo "<p class='text-success'>✓ تم تحديث المستخدم admin</p>";
    }
    
    // 7. إضافة بعض الكورسات التجريبية
    echo "<p>7. إضافة الكورسات التجريبية...</p>";
    $courses = [
        ['دورة البرمجة الأساسية', 'PROG101', 'دورة تعليم أساسيات البرمجة للمبتدئين', 40, 500.00, 'د. أحمد محمد'],
        ['دورة التصميم الجرافيكي', 'DESIGN101', 'دورة تعليم أساسيات التصميم الجرافيكي', 30, 400.00, 'أ. فاطمة علي'],
        ['دورة إدارة المشاريع', 'PM101', 'دورة في إدارة المشاريع وفق المعايير الدولية', 35, 600.00, 'د. محمد سالم']
    ];
    
    foreach ($courses as $course) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM courses WHERE course_code = ?");
        $stmt->execute([$course[1]]);
        if ($stmt->fetch()['count'] == 0) {
            $stmt = $pdo->prepare("INSERT INTO courses (course_name, course_code, description, duration_hours, price, instructor_name, max_students) VALUES (?, ?, ?, ?, ?, ?, 30)");
            $stmt->execute($course);
        }
    }
    echo "<p class='text-success'>✓ تم إضافة الكورسات التجريبية</p>";
    
    echo "<div class='alert alert-success mt-4'>";
    echo "<h5>✅ تم إصلاح جميع المشاكل بنجاح!</h5>";
    echo "<hr>";
    echo "<h6>بيانات تسجيل الدخول:</h6>";
    echo "<p><strong>اسم المستخدم:</strong> admin</p>";
    echo "<p><strong>كلمة المرور:</strong> admin123</p>";
    echo "<div class='mt-3'>";
    echo "<a href='login.php' class='btn btn-primary me-2'>تسجيل الدخول</a>";
    echo "<a href='debug_login.php' class='btn btn-info'>تشخيص إضافي</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>خطأ في الإصلاح:</h5>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h6>تأكد من:</h6>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL في XAMPP</li>";
    echo "<li>صحة إعدادات قاعدة البيانات</li>";
    echo "<li>وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "        </div>
        </div>
    </div>
</body>
</html>";
?>
