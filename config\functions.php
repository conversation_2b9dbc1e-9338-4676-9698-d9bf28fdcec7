<?php
/**
 * ملف الدوال المساعدة والأمان
 * Helper Functions and Security
 */

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

/**
 * التحقق من تسجيل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * التحقق من صلاحيات المستخدم
 */
function hasPermission($required_role = 'staff') {
    if (!isLoggedIn()) {
        return false;
    }
    
    $user_role = $_SESSION['user_role'] ?? 'staff';
    
    if ($required_role === 'admin') {
        return $user_role === 'admin';
    }
    
    return true; // staff أو admin
}

/**
 * إعادة توجيه إلى صفحة تسجيل الدخول
 */
function redirectToLogin() {
    header('Location: login.php');
    exit();
}

/**
 * التحقق من الصلاحيات وإعادة التوجيه
 */
function requireLogin($required_role = 'staff') {
    if (!isLoggedIn()) {
        redirectToLogin();
    }
    
    if (!hasPermission($required_role)) {
        header('Location: dashboard.php?error=no_permission');
        exit();
    }
}

/**
 * إنشاء CSRF Token
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * التحقق من CSRF Token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * التحقق من صحة رقم الهاتف
 */
function validatePhone($phone) {
    return preg_match('/^[0-9+\-\s()]{10,20}$/', $phone);
}

/**
 * رفع الملفات بأمان
 */
function uploadFile($file, $allowed_types, $upload_dir = 'uploads/') {
    // التحقق من وجود الملف
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('خطأ في رفع الملف');
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > MAX_FILE_SIZE) {
        throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
    }
    
    // التحقق من نوع الملف
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_types)) {
        throw new Exception('نوع الملف غير مسموح');
    }
    
    // إنشاء اسم ملف فريد
    $new_filename = uniqid() . '_' . time() . '.' . $file_extension;
    $upload_path = $upload_dir . $new_filename;
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // نقل الملف
    if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
        throw new Exception('فشل في حفظ الملف');
    }
    
    return $new_filename;
}

/**
 * حذف ملف
 */
function deleteFile($filename, $upload_dir = 'uploads/') {
    $file_path = $upload_dir . $filename;
    if (file_exists($file_path)) {
        return unlink($file_path);
    }
    return true;
}

/**
 * تنسيق التاريخ للعرض
 */
function formatDate($date, $format = 'Y-m-d H:i') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * تنسيق التاريخ بالعربية
 */
function formatDateArabic($date) {
    if (empty($date)) return '';
    
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "$day $month $year";
}

/**
 * إنشاء رسالة تنبيه
 */
function setAlert($message, $type = 'success') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * عرض رسالة التنبيه
 */
function showAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        
        $alert_class = '';
        switch ($alert['type']) {
            case 'success':
                $alert_class = 'alert-success';
                break;
            case 'error':
                $alert_class = 'alert-danger';
                break;
            case 'warning':
                $alert_class = 'alert-warning';
                break;
            case 'info':
                $alert_class = 'alert-info';
                break;
            default:
                $alert_class = 'alert-info';
        }
        
        echo '<div class="alert ' . $alert_class . ' alert-dismissible fade show" role="alert">';
        echo htmlspecialchars($alert['message']);
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
    }
}

/**
 * إنشاء pagination
 */
function createPagination($current_page, $total_pages, $base_url) {
    if ($total_pages <= 1) return '';
    
    $pagination = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';
    
    // Previous button
    if ($current_page > 1) {
        $prev_page = $current_page - 1;
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $base_url . '&page=' . $prev_page . '">السابق</a></li>';
    }
    
    // Page numbers
    $start = max(1, $current_page - 2);
    $end = min($total_pages, $current_page + 2);
    
    for ($i = $start; $i <= $end; $i++) {
        $active = ($i == $current_page) ? 'active' : '';
        $pagination .= '<li class="page-item ' . $active . '"><a class="page-link" href="' . $base_url . '&page=' . $i . '">' . $i . '</a></li>';
    }
    
    // Next button
    if ($current_page < $total_pages) {
        $next_page = $current_page + 1;
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $base_url . '&page=' . $next_page . '">التالي</a></li>';
    }
    
    $pagination .= '</ul></nav>';
    return $pagination;
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, HASH_ALGO);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}
?>
