<?php
/**
 * صفحة تسجيل الدخول
 * Login Page
 */

require_once 'config/database.php';
require_once 'config/functions.php';

// إذا كان المستخدم مسجل دخول، إعادة توجيه إلى لوحة التحكم
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit();
}

$error_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // التحقق من CSRF Token
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'خطأ في التحقق من الأمان';
    } elseif (empty($username) || empty($password)) {
        $error_message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            // البحث عن المستخدم
            $sql = "SELECT id, username, password, full_name, email, role, is_active 
                    FROM users 
                    WHERE username = ? AND is_active = 1";
            $user = getDB()->fetch($sql, [$username]);
            
            if ($user && verifyPassword($password, $user['password'])) {
                // تسجيل دخول ناجح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['login_time'] = time();
                
                // تسجيل العملية
                logActivity($user['id'], 'login');
                
                // إعادة توجيه إلى لوحة التحكم
                header('Location: dashboard.php');
                exit();
            } else {
                $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                
                // تسجيل محاولة دخول فاشلة
                logActivity(null, 'failed_login', 'users', null, null, ['username' => $username]);
            }
        } catch (Exception $e) {
            error_log("Login Error: " . $e->getMessage());
            $error_message = 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة لاحقاً';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - مركز التدريب وخدمة المجتمع</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <!-- Logo and Title -->
                        <div class="text-center mb-4">
                            <i class="bi bi-mortarboard-fill text-primary" style="font-size: 3rem;"></i>
                            <h3 class="mt-3 mb-1">مركز التدريب وخدمة المجتمع</h3>
                            <p class="text-muted">لوحة التحكم الإدارية</p>
                        </div>
                        
                        <!-- Error Message -->
                        <?php if (!empty($error_message)): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <?php echo htmlspecialchars($error_message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Login Form -->
                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person-fill me-1"></i>
                                    اسم المستخدم
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="username" 
                                       name="username" 
                                       value="<?php echo htmlspecialchars($username ?? ''); ?>"
                                       required 
                                       autocomplete="username"
                                       placeholder="أدخل اسم المستخدم">
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock-fill me-1"></i>
                                    كلمة المرور
                                </label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password" 
                                           required 
                                           autocomplete="current-password"
                                           placeholder="أدخل كلمة المرور">
                                    <button class="btn btn-outline-secondary" 
                                            type="button" 
                                            id="togglePassword">
                                        <i class="bi bi-eye-fill"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </div>
                        </form>
                        
                        <!-- Footer -->
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                © <?php echo date('Y'); ?> مركز التدريب وخدمة المجتمع
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Demo Credentials -->
                <div class="card mt-3 border-info">
                    <div class="card-body text-center">
                        <h6 class="card-title text-info">
                            <i class="bi bi-info-circle-fill me-1"></i>
                            بيانات تجريبية
                        </h6>
                        <p class="card-text small mb-1">
                            <strong>اسم المستخدم:</strong> admin
                        </p>
                        <p class="card-text small mb-0">
                            <strong>كلمة المرور:</strong> admin123
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.className = 'bi bi-eye-slash-fill';
            } else {
                passwordField.type = 'password';
                icon.className = 'bi bi-eye-fill';
            }
        });
        
        // Auto focus on username field
        document.getElementById('username').focus();
    </script>
</body>
</html>
