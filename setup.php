<?php
/**
 * ملف إعداد النظام وإنشاء قاعدة البيانات
 * System Setup and Database Creation
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_user = 'root';
$db_pass = '';
$db_name = 'training_center';
$db_charset = 'utf8mb4';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد النظام - مركز التدريب وخدمة المجتمع</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body class='bg-light'>
    <div class='container mt-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='card shadow'>
                    <div class='card-header bg-primary text-white'>
                        <h4 class='mb-0'>
                            <i class='bi bi-gear-fill me-2'></i>
                            إعداد نظام مركز التدريب وخدمة المجتمع
                        </h4>
                    </div>
                    <div class='card-body'>";

try {
    echo "<div class='alert alert-info'>
            <i class='bi bi-info-circle me-2'></i>
            جاري التحقق من الاتصال بقاعدة البيانات...
          </div>";
    
    // محاولة الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $dsn = "mysql:host=$db_host;charset=$db_charset";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    
    $pdo = new PDO($dsn, $db_user, $db_pass, $options);
    
    echo "<div class='alert alert-success'>
            <i class='bi bi-check-circle me-2'></i>
            تم الاتصال بخادم MySQL بنجاح!
          </div>";
    
    // التحقق من وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE '$db_name'");
    $database_exists = $stmt->rowCount() > 0;
    
    if (!$database_exists) {
        echo "<div class='alert alert-warning'>
                <i class='bi bi-exclamation-triangle me-2'></i>
                قاعدة البيانات '$db_name' غير موجودة. جاري إنشاؤها...
              </div>";
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE `$db_name` CHARACTER SET $db_charset COLLATE {$db_charset}_unicode_ci");
        
        echo "<div class='alert alert-success'>
                <i class='bi bi-check-circle me-2'></i>
                تم إنشاء قاعدة البيانات '$db_name' بنجاح!
              </div>";
    } else {
        echo "<div class='alert alert-info'>
                <i class='bi bi-info-circle me-2'></i>
                قاعدة البيانات '$db_name' موجودة مسبقاً.
              </div>";
    }
    
    // الاتصال بقاعدة البيانات المحددة
    $dsn = "mysql:host=$db_host;dbname=$db_name;charset=$db_charset";
    $pdo = new PDO($dsn, $db_user, $db_pass, $options);
    
    echo "<div class='alert alert-success'>
            <i class='bi bi-check-circle me-2'></i>
            تم الاتصال بقاعدة البيانات '$db_name' بنجاح!
          </div>";
    
    // قراءة ملف SQL وتنفيذه
    $sql_file = 'database.sql';
    if (file_exists($sql_file)) {
        echo "<div class='alert alert-info'>
                <i class='bi bi-info-circle me-2'></i>
                جاري إنشاء الجداول...
              </div>";
        
        $sql_content = file_get_contents($sql_file);
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sql_content);
        $executed_queries = 0;
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query) && !preg_match('/^--/', $query)) {
                try {
                    $pdo->exec($query);
                    $executed_queries++;
                } catch (PDOException $e) {
                    // تجاهل أخطاء الجداول الموجودة مسبقاً
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "<div class='alert alert-warning'>
                                <i class='bi bi-exclamation-triangle me-2'></i>
                                تحذير في الاستعلام: " . htmlspecialchars($e->getMessage()) . "
                              </div>";
                    }
                }
            }
        }
        
        echo "<div class='alert alert-success'>
                <i class='bi bi-check-circle me-2'></i>
                تم تنفيذ $executed_queries استعلام بنجاح!
              </div>";
    } else {
        echo "<div class='alert alert-warning'>
                <i class='bi bi-exclamation-triangle me-2'></i>
                ملف database.sql غير موجود. يرجى التأكد من وجود الملف.
              </div>";
    }
    
    // التحقق من الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (count($tables) > 0) {
        echo "<div class='alert alert-success'>
                <i class='bi bi-check-circle me-2'></i>
                تم إنشاء " . count($tables) . " جدول بنجاح:
                <ul class='mt-2 mb-0'>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul></div>";
    }
    
    // التحقق من المستخدم الافتراضي
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
    $admin_exists = $stmt->fetch()['count'] > 0;
    
    if ($admin_exists) {
        echo "<div class='alert alert-info'>
                <i class='bi bi-person-check me-2'></i>
                المستخدم الافتراضي 'admin' موجود.
              </div>";
    } else {
        echo "<div class='alert alert-warning'>
                <i class='bi bi-exclamation-triangle me-2'></i>
                المستخدم الافتراضي غير موجود. تحقق من ملف database.sql.
              </div>";
    }
    
    // إنشاء مجلدات الملفات المرفوعة
    $upload_dirs = ['uploads', 'uploads/payments', 'uploads/id_cards'];
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "<div class='alert alert-success'>
                        <i class='bi bi-folder-plus me-2'></i>
                        تم إنشاء مجلد: $dir
                      </div>";
            } else {
                echo "<div class='alert alert-danger'>
                        <i class='bi bi-exclamation-triangle me-2'></i>
                        فشل في إنشاء مجلد: $dir
                      </div>";
            }
        } else {
            echo "<div class='alert alert-info'>
                    <i class='bi bi-folder-check me-2'></i>
                    المجلد موجود: $dir
                  </div>";
        }
    }
    
    echo "<div class='alert alert-success'>
            <h5><i class='bi bi-check-circle me-2'></i>تم إعداد النظام بنجاح!</h5>
            <hr>
            <p class='mb-2'><strong>بيانات تسجيل الدخول:</strong></p>
            <ul class='mb-3'>
                <li><strong>اسم المستخدم:</strong> admin</li>
                <li><strong>كلمة المرور:</strong> admin123</li>
            </ul>
            <a href='login.php' class='btn btn-primary'>
                <i class='bi bi-box-arrow-in-right me-2'></i>
                الانتقال إلى صفحة تسجيل الدخول
            </a>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='bi bi-exclamation-triangle me-2'></i>خطأ في قاعدة البيانات</h5>
            <p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
            <hr>
            <h6>الحلول المقترحة:</h6>
            <ul>
                <li>تأكد من تشغيل خادم MySQL</li>
                <li>تحقق من بيانات الاتصال (اسم المستخدم وكلمة المرور)</li>
                <li>تأكد من أن المستخدم له صلاحيات إنشاء قواعد البيانات</li>
                <li>في XAMPP: تأكد من تشغيل Apache و MySQL</li>
            </ul>
          </div>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
            <h5><i class='bi bi-exclamation-triangle me-2'></i>خطأ عام</h5>
            <p>" . htmlspecialchars($e->getMessage()) . "</p>
          </div>";
}

echo "          </div>
                </div>
                
                <div class='card mt-4'>
                    <div class='card-header'>
                        <h5 class='mb-0'>
                            <i class='bi bi-info-circle me-2'></i>
                            معلومات النظام
                        </h5>
                    </div>
                    <div class='card-body'>
                        <div class='row'>
                            <div class='col-md-6'>
                                <p><strong>إصدار PHP:</strong> " . PHP_VERSION . "</p>
                                <p><strong>خادم الويب:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف') . "</p>
                            </div>
                            <div class='col-md-6'>
                                <p><strong>المجلد الحالي:</strong> " . __DIR__ . "</p>
                                <p><strong>الوقت:</strong> " . date('Y-m-d H:i:s') . "</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
